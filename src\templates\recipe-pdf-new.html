<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{recipe_title}}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #1f2937;
            background: linear-gradient(135deg, #f9fafb 0%, #ffffff 100%);
            font-size: 10pt;
            margin: 0;
            padding: 0;
            font-weight: 400;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 100%;
            margin: 0;
            padding: 0;
            background: #ffffff;
            box-shadow: 0 0 50pt rgba(0, 0, 0, 0.05);
        }

        .main-content {
            width: 100%;
            padding: 12mm 10mm;
            padding-bottom: 8mm;
            background: #ffffff;
            border-radius: 8pt;
            margin: 4mm;
            box-shadow: 0 4pt 6pt -1pt rgba(0, 0, 0, 0.1);
        }

        /* Clean professional header with image support */
        .recipe-header {
            display: flex;
            align-items: flex-start;
            margin-bottom: 24pt;
            padding: 24pt;
            background: #ffffff;
            border: 1pt solid #e5e7eb;
            border-radius: 16pt;
            box-shadow: 0 10pt 25pt -5pt rgba(0, 0, 0, 0.1), 0 4pt 6pt -2pt rgba(0, 0, 0, 0.05);
            position: relative;
        }

        .recipe-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4pt;
            background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%);
            border-radius: 16pt 16pt 0 0;
        }

        .recipe-title-section {
            flex: 1;
        }

        .recipe-title {
            font-size: 22pt;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8pt;
            line-height: 1.2;
            letter-spacing: -0.5pt;
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .recipe-subtitle {
            font-size: 12pt;
            color: #64748b;
            font-weight: 400;
            margin-bottom: 6pt;
            line-height: 1.4;
        }

        .recipe-description {
            font-size: 10pt;
            color: #475569;
            line-height: 1.5;
            max-width: 85%;
        }

        /* Stunning recipe image container */
        .recipe-image-container {
            width: 160pt;
            height: 120pt;
            flex-shrink: 0;
            margin-left: 20pt;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12pt;
            position: relative;
            box-shadow: 0 6pt 25pt rgba(37, 99, 235, 0.2), 0 2pt 8pt rgba(0, 0, 0, 0.1);
            border: 2pt solid transparent;
            background: linear-gradient(145deg, #ffffff, #f8fafc);
            background-clip: padding-box;
        }

        .recipe-image-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(145deg, #3b82f6 0%, #2563eb 100%);
            border-radius: 12pt;
            z-index: -1;
            margin: -2pt;
        }

        /* Only show background for empty containers or containers with broken images */
        .recipe-image-container:empty,
        .recipe-image-container.no-image {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .recipe-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: cover;
            border-radius: 10pt;
            display: block;
            box-shadow: 0 4pt 20pt rgba(0, 0, 0, 0.3), 0 2pt 8pt rgba(37, 99, 235, 0.2);
            border: 2pt solid rgba(255, 255, 255, 0.9);
        }

        /* Enhanced placeholder styling when no image is present */
        .recipe-image-container:empty::before,
        .recipe-image-container.no-image::before {
            content: "📷\ARECIPE\AIMAGE";
            white-space: pre-line;
            text-align: center;
            font-size: 8pt;
            font-weight: 600;
            color: #64748b;
            line-height: 1.2;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
        }

        /* Hide broken images and show placeholder */
        .recipe-image[src=""],
        .recipe-image:not([src]),
        .recipe-image[src*="undefined"],
        .recipe-image[src*="null"] {
            display: none;
        }

        .recipe-image-container:has(.recipe-image[src=""]),
        .recipe-image-container:has(.recipe-image:not([src])),
        .recipe-image-container:has(.recipe-image[src*="undefined"]),
        .recipe-image-container:has(.recipe-image[src*="null"]) {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }

        .recipe-image-container:has(.recipe-image[src=""]):before,
        .recipe-image-container:has(.recipe-image:not([src])):before,
        .recipe-image-container:has(.recipe-image[src*="undefined"]):before,
        .recipe-image-container:has(.recipe-image[src*="null"]):before {
            content: "📷\ARECIPE\AIMAGE";
            white-space: pre-line;
            text-align: center;
            font-size: 8pt;
            font-weight: 600;
            color: #64748b;
            line-height: 1.2;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100%;
        }

        /* Main content centered layout */
        .main-content-centered {
            width: 100%;
            margin-top: 8pt;
        }

        /* Info sections grid layout for Storage, Preparation, Cooking, Allergens */
        .info-sections-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 6pt;
            margin-bottom: 8pt;
        }

        /* HACCP bottom section styling */
        .haccp-bottom-section {
            background: #fef3c7;
            border: 0.5pt solid #f59e0b;
            border-radius: 4pt;
            padding: 8pt;
            margin-top: 8pt;
            margin-bottom: 8pt;
            width: 100%;
        }

        .haccp-bottom-section .section-title {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8pt;
            font-size: 11pt;
            text-transform: uppercase;
            letter-spacing: 0.025em;
            border-bottom: 0.5pt solid #e2e8f0;
            padding-bottom: 4pt;
        }

        .haccp-content {
            font-size: 9pt;
            line-height: 1.4;
            color: #374151;
        }



        /* Professional info cards */
        .recipe-info-cards {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8pt;
            margin-bottom: 8pt;
            page-break-inside: avoid;
            break-inside: avoid;
        }

        .info-card {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 0.5pt solid #cbd5e1;
            border-radius: 6pt;
            padding: 10pt 8pt;
            text-align: center;
            box-shadow: 0 1pt 2pt rgba(0, 0, 0, 0.05);
        }

        .info-card-label {
            font-size: 8pt;
            color: #64748b;
            margin-bottom: 4pt;
            text-transform: uppercase;
            font-weight: 500;
            letter-spacing: 0.025em;
        }

        .info-card-value {
            font-size: 11pt;
            font-weight: 600;
            color: #2563eb;
            line-height: 1.2;
        }

        /* Professional allergens section */
        .allergens-tags-section {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 0.5pt solid #f59e0b;
            border-radius: 6pt;
            padding: 8pt;
            margin-bottom: 8pt;
            box-shadow: 0 1pt 3pt rgba(0, 0, 0, 0.1);
            page-break-inside: avoid;
            break-inside: avoid;
        }

        .allergens-tags-title {
            font-size: 9pt;
            font-weight: 600;
            color: #92400e;
            margin-bottom: 6pt;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .allergens-icons {
            display: flex;
            gap: 4pt;
            margin-bottom: 6pt;
        }

        .allergen-icon {
            width: 16pt;
            height: 16pt;
            background: #f59e0b;
            border-radius: 2pt;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 8pt;
            color: white;
            font-weight: 600;
        }

        .tags-text {
            font-size: 9pt;
            color: #92400e;
            line-height: 1.4;
        }

        /* Professional method section with enhanced page break controls */
        .method-section {
            margin-bottom: 8pt;
        }

        .method-step {
            margin-bottom: 16pt;
            padding: 20pt;
            background: #ffffff;
            border-left: 4pt solid #3b82f6;
            border-radius: 12pt;
            font-size: 9pt;
            line-height: 1.6;
            box-shadow: 0 4pt 6pt -1pt rgba(0, 0, 0, 0.1), 0 2pt 4pt -1pt rgba(0, 0, 0, 0.06);
            page-break-inside: avoid;
            break-inside: avoid;
            orphans: 3;
            widows: 3;
            min-height: 50pt;
            border: 1pt solid #f3f4f6;
            position: relative;
        }

        .method-step:last-child {
            margin-bottom: 0;
        }

        /* Ensure method steps with images stay together */
        .method-step-with-image {
            page-break-inside: avoid;
            break-inside: avoid;
            display: flex;
            flex-direction: column;
        }

        .step-header {
            margin-bottom: 6pt;
            font-weight: 600;
            color: #1e293b;
        }

        .step-content {
            display: flex;
            align-items: flex-start;
            gap: 8pt;
        }

        .step-description {
            flex: 1;
            color: #374151;
        }

        /* Step image container - restored for visibility */
        .step-image-container {
            width: 60pt;
            height: 45pt;
            margin-right: 8pt;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 3pt;
            background: #f8fafc;
        }

        .step-image {
            max-width: 100%;
            max-height: 100%;
            object-fit: cover;
            border-radius: 2pt;
        }

        /* Modern & Attractive Resources Section */
        .resources-section {
            margin-bottom: 20pt;
            background: #ffffff;
            border: 1pt solid #e5e7eb;
            border-radius: 16pt;
            padding: 24pt;
            box-shadow: 0 10pt 25pt -5pt rgba(0, 0, 0, 0.1), 0 4pt 6pt -2pt rgba(0, 0, 0, 0.05);
            position: relative;
        }

        .resources-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4pt;
            background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%);
            border-radius: 16pt 16pt 0 0;
        }

        .resource-card {
            margin-bottom: 8px;
            padding: 6px;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            font-size: 11px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .resource-header {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
            font-weight: bold;
        }

        .resource-icon {
            margin-right: 6px;
            font-size: 13px;
        }

        .resource-title {
            color: #135e96;
            font-size: 12px;
        }

        .resource-description {
            margin-bottom: 6px;
            font-size: 11px;
            color: #666;
            font-style: italic;
        }

        .resource-image {
            width: 100%;
            max-width: 120px;
            height: auto;
            max-height: 60px;
            object-fit: contain;
            border-radius: 3px;
            display: block;
            margin: 0 auto;
        }

        .resource-link {
            text-align: center;
            margin-top: 4px;
        }

        .resource-link a {
            color: #135e96 !important;
            text-decoration: none !important;
            font-weight: 600 !important;
            background: #e3f2fd;
            padding: 4px 8px;
            border-radius: 4px;
            border: 1px solid #135e96;
            display: inline-block;
            font-size: 10px;
        }

        .image-fallback {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 12px;
            background: #f5f5f5;
            border-radius: 4px;
            border: 1px dashed #ccc;
            font-size: 11px;
        }

        /* New compact resource styles */
        .resources-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16pt;
            align-items: stretch;
        }

        .resource-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12pt;
            padding: 20pt;
            background: #ffffff;
            border: 1pt solid #f3f4f6;
            border-radius: 12pt;
            box-shadow: 0 4pt 6pt -1pt rgba(0, 0, 0, 0.1), 0 2pt 4pt -1pt rgba(0, 0, 0, 0.06);
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .resource-item:hover {
            transform: translateY(-2pt);
            box-shadow: 0 10pt 15pt -3pt rgba(0, 0, 0, 0.1), 0 4pt 6pt -2pt rgba(0, 0, 0, 0.05);
        }

        .resource-image-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12pt;
            padding: 20pt;
            background: #ffffff;
            border: 1pt solid #f3f4f6;
            border-radius: 12pt;
            box-shadow: 0 4pt 6pt -1pt rgba(0, 0, 0, 0.1), 0 2pt 4pt -1pt rgba(0, 0, 0, 0.06);
            position: relative;
            overflow: hidden;
            transition: all 0.2s ease;
        }

        .resource-image-item:hover {
            transform: translateY(-2pt);
            box-shadow: 0 10pt 15pt -3pt rgba(0, 0, 0, 0.1), 0 4pt 6pt -2pt rgba(0, 0, 0, 0.05);
        }

        .resource-image {
            width: 140pt;
            height: 105pt;
            object-fit: cover;
            border-radius: 8pt;
            flex-shrink: 0;
            box-shadow: 0 4pt 8pt rgba(0, 0, 0, 0.1);
            border: 1pt solid #e5e7eb;
        }

        .resource-image-info {
            flex: 1;
            font-size: 11px;
            color: #374151;
        }

        .resource-image-title {
            font-weight: 600;
            margin-bottom: 2px;
        }

        .resource-link-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12pt;
            padding: 20pt;
            background: #ffffff;
            border: 1pt solid #f3f4f6;
            border-radius: 12pt;
            box-shadow: 0 4pt 6pt -1pt rgba(0, 0, 0, 0.1), 0 2pt 4pt -1pt rgba(0, 0, 0, 0.06);
            position: relative;
            overflow: hidden;
            text-align: center;
            transition: all 0.2s ease;
        }

        .resource-link-item:hover {
            transform: translateY(-2pt);
            box-shadow: 0 10pt 15pt -3pt rgba(0, 0, 0, 0.1), 0 4pt 6pt -2pt rgba(0, 0, 0, 0.05);
        }

        .resource-icon {
            font-size: 32pt;
            margin-bottom: 8pt;
            color: #6b7280;
        }

        .resource-title {
            font-weight: 600;
            color: #1f2937;
            font-size: 11pt;
            margin-bottom: 8pt;
            text-align: center;
        }

        .resource-link-item a {
            color: #ffffff !important;
            text-decoration: none !important;
            font-weight: 500 !important;
            font-size: 9pt;
            padding: 8pt 20pt;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border-radius: 6pt;
            box-shadow: 0 2pt 4pt rgba(59, 130, 246, 0.3);
            display: inline-block;
            transition: all 0.2s ease;
            border: none;
        }

        .resource-link-item a:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
            transform: translateY(-1pt);
            box-shadow: 0 4pt 8pt rgba(59, 130, 246, 0.4);
        }

        /* Hidden class */
        .hidden {
            display: none !important;
        }

        /* HACCP items */
        .haccp-item {
            margin-bottom: 10px;
            padding: 8px;
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            font-size: 12px;
            line-height: 1.4;
        }

        /* Menu section */
        .menu-section {
            margin-bottom: 15px;
        }

        .menu-title {
            font-weight: bold;
            color: #135e96;
            margin-bottom: 10px;
            font-size: 18px;
        }

        .menu-list {
            font-size: 14px;
            line-height: 1.4;
        }

        /* Professional costs and yield sections - Enhanced backgrounds */
        .costs-yield-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8pt;
            margin-bottom: 8pt;
        }

        .cost-section {
            background: #f0fdf4;
            border: 0.5pt solid #22c55e;
            border-radius: 4pt;
            padding: 6pt;
        }

        .yield-section {
            background: #fef3c7;
            border: 0.5pt solid #f59e0b;
            border-radius: 4pt;
            padding: 6pt;
        }

        .section-title {
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 16pt;
            font-size: 14pt;
            text-transform: uppercase;
            letter-spacing: 1pt;
            text-align: center;
            position: relative;
            padding: 12pt 0;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60pt;
            height: 3pt;
            background: linear-gradient(90deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%);
            border-radius: 2pt;
        }

        .cost-item,
        .yield-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4pt;
            font-size: 9pt;
            line-height: 1.4;
        }

        .cost-item:last-child,
        .yield-item:last-child {
            margin-bottom: 0;
        }

        /* Professional ingredients table */
        .ingredients-section {
            margin-bottom: 8pt;
        }

        .ingredients-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            font-size: 10pt;
            border: 2pt solid #f1f5f9;
            border-radius: 16pt;
            overflow: hidden;
            table-layout: fixed;
            box-shadow: 0 20pt 25pt -5pt rgba(0, 0, 0, 0.1), 0 10pt 10pt -5pt rgba(0, 0, 0, 0.04);
            background: #ffffff;
            margin: 0 auto;
        }

        .ingredients-table th {
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: #ffffff;
            padding: 16pt 12pt;
            text-align: center;
            font-weight: 700;
            font-size: 10pt;
            text-transform: uppercase;
            letter-spacing: 1pt;
            border: none;
            position: relative;
            text-shadow: 0 1pt 2pt rgba(0, 0, 0, 0.3);
        }

        .ingredients-table th:first-child {
            border-top-left-radius: 14pt;
        }

        .ingredients-table th:last-child {
            border-top-right-radius: 14pt;
        }

        .ingredients-table th:first-child {
            text-align: center;
            /* Ingredient name header center-aligned */
        }

        .ingredients-table td {
            padding: 14pt 12pt;
            border: none;
            vertical-align: middle;
            line-height: 1.5;
            text-align: center;
            font-size: 10pt;
            font-weight: 500;
            color: #374151;
            word-wrap: break-word;
            overflow-wrap: break-word;
            position: relative;
        }

        .ingredients-table tbody tr {
            background: #ffffff;
            transition: all 0.2s ease;
            border-bottom: 1pt solid #f1f5f9;
        }

        .ingredients-table tbody tr:nth-child(even) {
            background: #f8fafc;
        }

        .ingredients-table tbody tr:hover {
            background: #e0f2fe;
            transform: scale(1.005);
            box-shadow: 0 4pt 8pt rgba(0, 0, 0, 0.1);
        }

        .ingredients-table tbody tr:last-child {
            border-bottom: none;
        }

        .ingredients-table tbody tr:last-child td:first-child {
            border-bottom-left-radius: 14pt;
        }

        .ingredients-table tbody tr:last-child td:last-child {
            border-bottom-right-radius: 14pt;
        }

        .ingredients-table tbody tr:nth-child(even) td {
            background: #f8fafc;
        }

        .ingredients-table tbody tr:hover {
            background: #f1f5f9;
        }

        /* Clean table borders */
        .ingredients-table {
            border: 1pt solid #d1d5db;
        }

        .ingredients-table th {
            border: 1pt solid #374151;
        }

        .ingredients-table td {
            border: 1pt solid #d1d5db;
        }

        /* Prevent awkward table row breaks */
        .ingredients-table tbody tr {
            page-break-inside: avoid;
            break-inside: avoid;
        }

        /* Perfect ingredient column styling */
        .ingredient-name {
            font-weight: 700;
            color: #1f2937;
            text-align: left !important;
            width: 30%;
            padding-left: 20pt !important;
            font-size: 10pt;
            display: flex;
            align-items: center;
            min-height: 28pt;
        }

        .ingredient-name strong {
            font-weight: 700;
            color: #1f2937;
        }

        .ingredient-quantity {
            font-weight: 600;
            color: #374151;
            text-align: center;
            width: 20%;
            font-size: 10pt;
        }

        .ingredient-cost {
            font-family: 'Inter', sans-serif;
            font-weight: 700;
            color: #059669;
            text-align: center;
            width: 15%;
            font-size: 10pt;
        }

        .ingredient-wastage {
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            color: #dc2626;
            text-align: center;
            width: 15%;
            font-size: 10pt;
        }

        .ingredient-final-cost {
            font-family: 'Inter', sans-serif;
            font-weight: 800;
            color: #1f2937;
            text-align: center;
            width: 20%;
            font-size: 11pt;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-radius: 8pt;
            padding: 8pt 4pt;
            border: 1pt solid #0ea5e9;
        }



        /* Professional right column sections - Optimized spacing */
        .haccp-section {
            background: #fef3c7;
            border: 0.5pt solid #f59e0b;
            border-radius: 4pt;
            padding: 6pt;
            margin-bottom: 4pt;
        }

        .storage-section,
        .preparation-section,
        .cooking-section {
            background: #eff6ff;
            border: 0.5pt solid #3b82f6;
            border-radius: 4pt;
            padding: 6pt;
            margin-bottom: 4pt;
        }

        .allergens-section {
            background: #fef3c7;
            border: 0.5pt solid #f59e0b;
            border-radius: 4pt;
            padding: 6pt;
            margin-bottom: 4pt;
        }

        .section-header {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 4pt;
            font-size: 9pt;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .section-content {
            font-size: 8pt;
            line-height: 1.4;
            color: #374151;
        }

        .haccp-item {
            margin-bottom: 4px;
            padding: 4px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 3px;
            border-left: 2px solid #ffc107;
        }

        .haccp-item strong {
            color: #135e96;
            display: block;
            margin-bottom: 2px;
        }

        .allergen-item {
            display: flex;
            align-items: center;
            margin-bottom: 3px;
            font-size: 11px;
        }

        .allergen-icon {
            width: 16px;
            height: 16px;
            margin-right: 6px;
            border-radius: 3px;
        }

        /* Footer - Handled by Puppeteer, hide in HTML */
        .footer {
            display: none;
        }

        .footer-logo {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .page-info {
            text-align: right;
        }

        /* Professional nutrition section */
        .nutrition-section {
            margin-bottom: 16pt;
        }

        .nutrition-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 8pt;
            border: 0.5pt solid #e2e8f0;
            border-radius: 4pt;
            overflow: hidden;
        }

        .nutrition-table th {
            background: #1e293b;
            color: white;
            padding: 6pt 4pt;
            text-align: center;
            font-weight: 600;
            font-size: 8pt;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .nutrition-table td {
            padding: 4pt;
            border-bottom: 0.5pt solid #e2e8f0;
            text-align: center;
            font-size: 8pt;
            line-height: 1.4;
        }

        .nutrition-table tbody tr:nth-child(even) {
            background: #f8fafc;
        }

        .tips-section {
            margin-bottom: 8pt;
            page-break-inside: avoid;
        }

        .tips-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8pt;
        }

        .tip-card {
            background: #fefce8;
            border: 0.5pt solid #eab308;
            border-radius: 4pt;
            padding: 8pt;
            page-break-inside: avoid;
        }

        .tip-header {
            display: flex;
            align-items: center;
            margin-bottom: 4pt;
        }

        .tip-icon {
            margin-right: 4pt;
            font-size: 10pt;
        }

        .tip-title {
            font-weight: 600;
            color: #1e293b;
            font-size: 9pt;
        }

        .tip-content {
            font-size: 8pt;
            line-height: 1.5;
            color: #374151;
        }

        .serving-section {
            background: #f0fdf4;
            border: 0.5pt solid #16a34a;
            border-radius: 4pt;
            padding: 6pt;
            margin-bottom: 8pt;
            page-break-inside: avoid;
        }

        .serving-content {
            font-size: 9pt;
            line-height: 1.5;
            color: #374151;
        }

        /* Prevent premature page breaks */
        .method-section,
        .resources-section,
        .tips-section,
        .serving-section {
            page-break-inside: avoid;
            break-inside: avoid;
        }

        .ingredients-section {
            page-break-inside: auto;
            /* Allow ingredients table to break if needed */
        }

        /* Ensure content flows naturally */
        .main-content-centered {
            page-break-inside: auto;
        }

        .info-sections-grid {
            page-break-inside: avoid;
            break-inside: avoid;
        }

        .haccp-bottom-section {
            page-break-inside: avoid;
            break-inside: avoid;
        }

        /* Page layout - margins handled by Puppeteer */
        @page {
            size: A4;
        }

        /* Dynamic page breaks */
        .page-break-before {
            page-break-before: always;
        }

        .page-break-after {
            page-break-after: always;
        }

        /* Avoid breaking inside these elements */
        .avoid-break {
            page-break-inside: avoid;
            break-inside: avoid;
        }

        /* Print optimizations for dynamic content flow */
        @media print {
            body {
                margin: 0;
                padding: 0;
            }

            .container {
                padding: 0;
                margin: 0;
            }

            .main-content {
                padding: 0 5mm;
                padding-bottom: 0;
                /* Remove bottom padding as footer is handled by @page */
            }

            /* Keep sections together when possible */
            .recipe-header,
            .recipe-info-cards,
            .allergens-tags-section,
            .cost-section,
            .yield-section,
            .tips-section,
            .serving-section,
            .method-section,
            .resources-section,
            .info-sections-grid,
            .haccp-bottom-section {
                page-break-inside: avoid;
                break-inside: avoid;
            }

            /* Allow ingredients table to break naturally across pages */
            .ingredients-table {
                page-break-inside: auto;
            }

            /* Footer hidden - handled by Puppeteer */
            .footer {
                display: none;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="main-content">
            <!-- Header with title and image -->
            <div class="recipe-header">
                <div class="recipe-title-section">
                    <h1 class="recipe-title">{{recipe_title}}</h1>
                    {{#if recipe_public_title}}
                    <div class="recipe-subtitle">{{recipe_public_title}}</div>
                    {{/if}}
                    {{#if recipe_description}}
                    <div class="recipe-description">{{recipe_description}}</div>
                    {{/if}}
                </div>
                {{#if recipe_image_url}}
                <div class="recipe-image-container">
                    <img src="{{recipe_image_url}}" alt="{{recipe_title}}" class="recipe-image"
                        onerror="this.style.display='none'; this.parentElement.classList.add('no-image');"
                        onload="if(this.naturalWidth === 0 || this.naturalHeight === 0) { this.style.display='none'; this.parentElement.classList.add('no-image'); }">
                </div>
                {{else}}
                <div class="recipe-image-container no-image"></div>
                {{/if}}
            </div>

            <!-- Recipe Info Cards -->
            <div class="recipe-info-cards">
                <div class="info-card">
                    <div class="info-card-label">Prep Time</div>
                    <div class="info-card-value">{{#if recipe_preparation_time}}{{recipe_preparation_time}}
                        min{{else}}-{{/if}}</div>
                </div>
                <div class="info-card">
                    <div class="info-card-label">Cook Time</div>
                    <div class="info-card-value">{{#if recipe_cook_time}}{{recipe_cook_time}} min{{else}}-{{/if}}</div>
                </div>
                <div class="info-card">
                    <div class="info-card-label">Total Time</div>
                    <div class="info-card-value">{{#if recipe_preparation_time}}{{#if recipe_cook_time}}{{add
                        recipe_preparation_time recipe_cook_time}} min{{else}}{{recipe_preparation_time}}
                        min{{/if}}{{else}}{{#if recipe_cook_time}}{{recipe_cook_time}} min{{else}}-{{/if}}{{/if}}</div>
                </div>
                <div class="info-card">
                    <div class="info-card-label">Servings</div>
                    <div class="info-card-value">{{#if recipe_yield}}{{recipe_yield}}{{else}}{{#if
                        recipe_total_portions}}{{recipe_total_portions}}{{else}}-{{/if}}{{/if}}</div>
                </div>
            </div>

            <!-- Allergens and Tags -->
            <div class="allergens-tags-section">
                <div class="allergens-tags-title">Allergens & Dietary Information</div>
                <div class="allergens-icons">{{{allergen_icons}}}</div>
                <div class="tags-text">{{recipe_tags}}</div>
            </div>

            <!-- Main Content - Centered Layout -->
            <div class="main-content-centered">
                <!-- Costs and Yield -->
                <div class="costs-yield-grid">
                    <div class="cost-section">
                        <div class="section-title">Costs</div>
                        <div class="cost-item">
                            <span>Batch cost</span>
                            <span>{{batch_cost}}</span>
                        </div>
                        <div class="cost-item">
                            <span>Serving cost</span>
                            <span>{{serving_cost}}</span>
                        </div>
                    </div>
                    <div class="yield-section">
                        <div class="section-title">Yield</div>
                        <div class="yield-item">
                            <span>Cooked weight</span>
                            <span>{{cooked_weight}}</span>
                        </div>
                        <div class="yield-item">
                            <span>Serving size</span>
                            <span>{{serving_size}}</span>
                        </div>
                        <div class="yield-item">
                            <span>Servings per batch</span>
                            <span>{{servings_per_batch}}</span>
                        </div>
                    </div>
                </div>

                <!-- Ingredients Table -->
                <div class="ingredients-section">
                    <div class="section-title">Ingredients</div>
                    <table class="ingredients-table">
                        <thead>
                            <tr>
                                <th style="width: 30%;">Ingredient</th>
                                <th style="width: 20%;">Qty</th>
                                <th style="width: 15%;">Cost</th>
                                <th style="width: 15%;">Wastage %</th>
                                <th style="width: 20%;">Final Cost</th>
                            </tr>
                        </thead>
                        <tbody>
                            {{#each ingredients}}
                            <tr>
                                <td class="ingredient-name"><strong>{{ingredient_name}}</strong></td>
                                <td class="ingredient-quantity">{{ingredient_quantity}}{{#if measure_title}} {{measure_title}}{{/if}}</td>
                                <td class="ingredient-cost">{{formattedCost}}</td>
                                <td class="ingredient-wastage">{{formattedWastage}}</td>
                                <td class="ingredient-final-cost">{{formattedFinalCost}}</td>
                            </tr>
                            {{/each}}
                        </tbody>
                    </table>
                </div>

                <!-- Instructions Section -->
                <div class="method-section">
                    <div class="section-title">Instructions</div>
                    <div class="method-content">
                        {{#each steps}}
                        <div class="method-step">
                            <div class="step-header">
                                <strong>Step {{recipe_step_order}}:</strong>
                            </div>
                            <div class="step-content">
                                <div class="step-description">{{{recipe_step_description}}}</div>
                                {{#if item_detail.item_link}}
                                <div class="step-image-container">
                                    <img src="{{item_detail.item_link}}" alt="Step {{recipe_step_order}}"
                                        class="step-image" onerror="this.style.display='none'">
                                </div>
                                {{/if}}
                            </div>
                        </div>
                        {{/each}}
                    </div>
                </div>

                <!-- Resources Section -->
                {{#if resources}}
                <div class="resources-section">
                    <div class="section-title">Resources</div>
                    <div class="resources-content">
                        <div class="resources-container">
                            {{#each resources}}
                            {{#if (isImage this)}}
                            <div class="resource-image-item">
                                <img src="{{getResourceLink this}}" alt="Resource Image" class="resource-image"
                                    onerror="this.style.display='none'; this.parentElement.querySelector('.image-fallback').style.display='flex';">
                                <div class="image-fallback" style="display: none; flex-direction: column; align-items: center; justify-content: center; height: 105pt; color: #9ca3af; font-style: italic;">
                                    <div style="font-size: 24pt; margin-bottom: 8pt;">🖼️</div>
                                    <div style="font-size: 9pt;">Image not available</div>
                                </div>
                                <div class="resource-title">
                                    {{getResourceText this}}
                                </div>
                            </div>
                            {{else}}
                            <div class="resource-link-item">
                                <div class="resource-icon">{{getResourceIcon this}}</div>
                                <div class="resource-title">
                                    {{getResourceText this}}
                                </div>
                                <a href="{{getResourceLink this}}" target="_blank" rel="noopener noreferrer">
                                    View Resource
                                </a>
                            </div>
                            {{/if}}
                            {{/each}}
                        </div>
                    </div>
                </div>
                {{/if}}

                <!-- Storage, Preparation, Cooking, and Allergens in a grid layout -->
                <div class="info-sections-grid">
                    <!-- Storage Section -->
                    <div class="storage-section avoid-break">
                        <div class="section-header">Storage</div>
                        <div class="section-content">{{storage_content}}</div>
                    </div>

                    <!-- Preparation Section -->
                    <div class="preparation-section avoid-break">
                        <div class="section-header">Preparation</div>
                        <div class="section-content">{{preparation_content}}</div>
                    </div>

                    <!-- Cooking Section -->
                    <div class="cooking-section avoid-break">
                        <div class="section-header">Cooking</div>
                        <div class="section-content">{{cooking_content}}</div>
                    </div>

                    <!-- Allergens Section -->
                    {{#if allergens_content}}
                    <div class="allergens-section avoid-break">
                        <div class="section-header">Allergens</div>
                        <div class="section-content">{{{allergens_content}}}</div>
                    </div>
                    {{/if}}
                </div>

                <!-- Chef Tips and FOH Notes -->
                {{#if (or recipe_head_chef_tips recipe_foh_tips)}}
                <div class="tips-section">
                    <div class="section-title">Chef Tips & FOH Notes</div>
                    <div class="tips-grid">
                        {{#if recipe_head_chef_tips}}
                        <div class="tip-card">
                            <div class="tip-header">
                                <span class="tip-icon">👨‍🍳</span>
                                <span class="tip-title">Head Chef Notes</span>
                            </div>
                            <div class="tip-content">{{recipe_head_chef_tips}}</div>
                        </div>
                        {{/if}}
                        {{#if recipe_foh_tips}}
                        <div class="tip-card">
                            <div class="tip-header">
                                <span class="tip-icon">🏨</span>
                                <span class="tip-title">FOH Notes</span>
                            </div>
                            <div class="tip-content">{{recipe_foh_tips}}</div>
                        </div>
                        {{/if}}
                    </div>
                </div>
                {{/if}}

                <!-- Serving Instructions -->
                {{#if serving_instructions}}
                <div class="serving-section avoid-break">
                    <div class="section-title">Serving Instructions</div>
                    <div class="serving-content">{{serving_instructions}}</div>
                </div>
                {{/if}}
            </div>

            <!-- HACCP Section - Moved to bottom -->
            {{#if haccp_attributes}}
            <div class="haccp-bottom-section avoid-break">
                <div class="section-title">HACCP</div>
                <div class="haccp-content">
                    {{#each haccp_attributes}}
                    <div class="haccp-item">
                        <strong>{{attribute_title}}</strong>
                        {{#if attribute_description}}{{attribute_description}}{{/if}}
                    </div>
                    {{/each}}
                </div>
            </div>
            {{/if}}

            <!-- Nutrition Section - Dynamic content that flows naturally -->
            {{#if nutrition_attributes}}
            <div class="nutrition-section page-break-before">
                <div class="section-title">Detailed Nutrition Information</div>
                <div class="nutrition-grid">
                    <div class="nutrition-column">
                        <table class="nutrition-table">
                            <thead>
                                <tr>
                                    <th>Nutrient</th>
                                    <th>Per 100g</th>
                                    <th>Per Serving</th>
                                    <th>% Daily Value</th>
                                    <th>Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                {{#each nutrition_attributes}}
                                <tr>
                                    <td style="text-align: left; font-weight: bold;">{{attribute_title}}</td>
                                    <td>{{#if unit}}{{unit}}{{#if unit_of_measure}}
                                        {{unit_of_measure}}{{/if}}{{else}}-{{/if}}</td>
                                    <td>{{getPerServingValue this ../recipe_total_portions ../recipe_yield}}</td>
                                    <td>-</td>
                                    <td>-</td>
                                </tr>
                                {{/each}}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            {{/if}}
        </div>

        <!-- Footer - Will appear on every page automatically -->
        <div class="footer">
            <div class="footer-logo">
                <span>{{organization_name}}</span>
            </div>
            <div class="page-info">
                <div>Printed on {{generation_date}}</div>
                <div>Printed by: {{creator_user_full_name}}</div>
                <div>Page <span class="page-number"></span> of <span class="total-pages"></span></div>
            </div>
        </div>

    </div>

    <script>
        // Handle image loading issues
        document.addEventListener('DOMContentLoaded', function () {
            const images = document.querySelectorAll('.recipe-image');
            images.forEach(function (img) {
                // Check if image source is valid
                if (!img.src ||
                    img.src.includes('undefined') ||
                    img.src.includes('null') ||
                    img.src === '' ||
                    img.src === window.location.href) {
                    img.style.display = 'none';
                    img.parentElement.classList.add('no-image');
                }

                // Handle load errors
                img.onerror = function () {
                    this.style.display = 'none';
                    this.parentElement.classList.add('no-image');
                };

                // Handle successful loads but check for placeholder images
                img.onload = function () {
                    if (this.naturalWidth === 0 || this.naturalHeight === 0) {
                        this.style.display = 'none';
                        this.parentElement.classList.add('no-image');
                    }
                };
            });
        });
    </script>

</body>

</html>