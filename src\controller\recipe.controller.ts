import { Request, Response } from "express";
import { StatusCodes } from "http-status-codes";
import { Op } from "sequelize";
import { randomUUID } from "crypto";
import { sequelize, db } from "../models";
import { RecipeStatus } from "../models/Recipe";
import { RecipeCategoryStatus } from "../models/RecipeCategory";
import { RecipeAttributesStatus } from "../models/RecipeAttributes";
import { RecipeIngredientsStatus } from "../models/RecipeIngredients";
import { RecipeUserStatus } from "../models/RecipeUser";
import { RecipeBookmarkStatus } from "../models/RecipeBookmarks";
import { RecipeStepsStatus } from "../models/RecipeSteps";
import { RecipeResourceStatus } from "../models/RecipeResources";
import { RecipeHistoryAction } from "../models/RecipeHistory";
import { generateUniqueSlug, generateSlug } from "../helper/slugGenerator";

// Get models from db object to ensure associations are set up
const Recipe = db.Recipe;
const RecipeCategory = db.RecipeCategory;
const RecipeAttributes = db.RecipeAttributes;
const RecipeIngredients = db.RecipeIngredients;
const RecipeUser = db.RecipeUser;
const RecipeBookmarks = db.RecipeBookmarks;
const RecipeSteps = db.RecipeSteps;
const RecipeResources = db.RecipeResources;
const RecipeHistory = db.RecipeHistory;
const User = db.User;
const Item = db.Item;
import {
  getPaginatedItems,
  getPagination,
  RECIPE_FILE_UPLOAD_CONSTANT,
  getUser,
  getOrganizationLogo,
  getOrgName,
  validateModulePermission,
  getPlatformFromRequest,
  getOrganizationContactInfo,
} from "../helper/common";
import {
  getRecipeHighlight,
  hasRecentChanges,
  getBulkRecipeHighlights,
} from "../helper/recipe-highlight.helper";
import uploadService from "../helper/upload.service";
import {
  ADMIN_SIDE_USER,
  MODULE_SLUGS,
  PERMISSION_TYPES,
  ROLE_CONSTANT,
} from "../helper/constant";
import {
  createRecipeHistory,
  getRecipeQueryOptions,
  exportRecipesToExcel,
  exportRecipesToCSV,
  exportRecipeToPDF,
  addFileUrlsToRecipe,
  getRecipeByIdRaw,
  getRecipeBySlugRaw,
  getPublicRecipeByIdRaw,
  getPublicRecipeBySlugRaw,
  getRecipesListRaw,
  getRecipeInListFormat,
} from "../helper/recipe.helper";
import {
  updateRecipeCostTimestamp,
  updateRecipeNutritionTimestamp,
} from "../helper/timestamp.helper";
import {
  TransactionManager,
  FileOperationTracker,
  ErrorHandler,
} from "../helper/transaction.helper";
import { ValidationHelper } from "../helper/validation.helper";
import { filterPublicRecipeFields } from "../models/RecipePermission";
import settingsService from "../services/settings.service";
import * as ExcelJS from "exceljs";
import Settings, { SettingCategory } from "../models/Settings";

// Interface for error handling
interface CustomError {
  message: string;
  name?: string;
  fields?: string[];
  table?: string;
  value?: any;
}

// Helper function for i18n with fallbacks
const getTranslation = (
  res: Response,
  key: string,
  fallback: string
): string => {
  try {
    const translation = res.__(key);
    // If translation returns the key itself, use fallback
    return translation === key ? fallback : translation;
  } catch (_error) {
    return fallback;
  }
};

// Helper function to check if user can create/update recipes
const canCreateUpdateRecipes = (userRole: any): boolean => {
  if (!userRole) return false;

  return ADMIN_SIDE_USER.includes(userRole.role_name);
};

// Helper function to check if user can view all private recipes
const canViewAllPrivateRecipes = (userRole: any): boolean => {
  if (!userRole) return false;

  const privilegedRoles = [
    ROLE_CONSTANT.SUPER_ADMIN,
    ROLE_CONSTANT.ADMIN,
    ROLE_CONSTANT.DIRECTOR,
    ROLE_CONSTANT.HR,
    ROLE_CONSTANT.AREA_MANAGER,
    ROLE_CONSTANT.BRANCH_MANAGER,
    ROLE_CONSTANT.HOTEL_MANAGER,
    ROLE_CONSTANT.ACCOUNTANT,
  ];
  return privilegedRoles.includes(userRole.role_name);
};

// Helper function to generate detailed change descriptions for recipe history (like your reference)
const generateDetailedChangeDescription = (
  _action: string,
  fieldName: string,
  _oldValue: any,
  newValue: any,
  recipeName?: string
): string => {
  const changes: string[] = [];

  // Add main action line
  if (recipeName) {
    changes.push(`Recipe "${recipeName}" was updated.`);
  }

  switch (fieldName) {
    case "ingredients":
      if (newValue && Array.isArray(newValue)) {
        newValue.forEach((ingredient: any) => {
          if (ingredient.name) {
            changes.push(`Ingredient(s) "${ingredient.name}" added.`);
          }
          if (ingredient.quantity && ingredient.measure) {
            changes.push(
              `Weight before cooking "quantity: ${ingredient.quantity}, unit: ${ingredient.measure}" added.`
            );
            changes.push(
              `Weight after cooking "quantity: ${ingredient.quantity}, unit: ${ingredient.measure}" added.`
            );
          }
          if (ingredient.cost) {
            changes.push(`Cost per serving set to "${ingredient.cost}".`);
          }
        });
      }
      break;

    case "attributes":
    case "nutrition_attributes":
      if (newValue && Array.isArray(newValue)) {
        newValue.forEach((attr: any) => {
          if (attr.unit && attr.unit_of_measure) {
            const nutrientName = attr.description || attr.name || "Nutrient";
            changes.push(
              `Nutrient "${nutrientName}" set to "${attr.unit} ${attr.unit_of_measure}".`
            );
          }
        });
      }
      break;

    case "allergen_attributes":
      if (newValue) {
        if (Array.isArray(newValue)) {
          newValue.forEach((allergen: any) => {
            changes.push(
              `Allergen(s) "${allergen.name || allergen.id}" added.`
            );
          });
        } else if (newValue.contains || newValue.may_contain) {
          if (newValue.contains && newValue.contains.length > 0) {
            changes.push(
              `Allergen(s) "${newValue.contains.join(", ")}" added.`
            );
          }
          if (newValue.may_contain && newValue.may_contain.length > 0) {
            changes.push(
              `May contain allergen(s) "${newValue.may_contain.join(", ")}" added.`
            );
          }
        }
      }
      break;

    case "basic_info":
      if (newValue && typeof newValue === "object") {
        Object.keys(newValue).forEach((key) => {
          const value = newValue[key];
          if (value !== undefined && value !== null) {
            const fieldDisplayName = key
              .replace(/_/g, " ")
              .replace(/\b\w/g, (l) => l.toUpperCase());
            changes.push(`${fieldDisplayName} set to "${value}".`);
          }
        });
      }
      break;

    case "categories":
      if (newValue && Array.isArray(newValue)) {
        changes.push(`Categories updated with ${newValue.length} item(s).`);
      }
      break;

    case "steps":
      if (newValue && Array.isArray(newValue)) {
        changes.push(`Recipe steps updated with ${newValue.length} step(s).`);
      }
      break;

    case "resources":
      if (newValue && Array.isArray(newValue)) {
        changes.push(
          `Recipe resources updated with ${newValue.length} resource(s).`
        );
      }
      break;

    default:
      changes.push(
        `${fieldName.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())} updated.`
      );
  }

  return changes.join("\n");
};

/**
 * @description Create a new recipe with all relations
 * @route POST /api/v1/recipes/create
 * @access Private
 */
const createRecipe = async (req: Request, res: Response): Promise<any> => {
  // Enhanced transaction and file operation management
  const transactionManager = new TransactionManager();
  const fileTracker = new FileOperationTracker();

  try {
    // Input validation and sanitization
    const sanitizedBody = ValidationHelper.sanitizeInput(req.body);

    // Start transaction
    const transaction = await transactionManager.start();
    // Parse JSON fields from form data if they exist
    const parseJsonField = (field: any) => {
      if (typeof field === "string") {
        try {
          return JSON.parse(field);
        } catch {
          return field;
        }
      }
      return field;
    };

    const {
      recipe_title,
      recipe_public_title,
      recipe_description,
      recipe_preparation_time,
      recipe_cook_time,
      has_recipe_public_visibility,
      has_recipe_private_visibility,
      recipe_status,
      recipe_serve_in,
      recipe_complexity_level,
      recipe_garnish,
      recipe_head_chef_tips,
      recipe_foh_tips,
      recipe_impression,
      recipe_yield,
      recipe_yield_unit,
      recipe_total_portions,
      recipe_single_portion_size,
      recipe_serving_method,
      recipe_placeholder,
      vitamin_a,
      vitamin_c,
      calcium,
      iron,
      is_ingredient_cooking_method,
      is_preparation_method,
      is_cost_manual,
    } = req.body;

    // Parse JSON arrays from form data with validation
    const categories = parseJsonField(sanitizedBody.categories);
    const attributes = parseJsonField(sanitizedBody.attributes); // Legacy support
    const nutrition_attributes = parseJsonField(
      sanitizedBody.nutrition_attributes
    );
    const allergen_attributes = parseJsonField(
      sanitizedBody.allergen_attributes
    );
    const cuisine_attributes = parseJsonField(sanitizedBody.cuisine_attributes);
    const dietary_attributes = parseJsonField(sanitizedBody.dietary_attributes);
    const haccp_attributes = parseJsonField(sanitizedBody.haccp_attributes);
    const ingredients = parseJsonField(sanitizedBody.ingredients);
    const steps = parseJsonField(sanitizedBody.steps);
    const resources = parseJsonField(sanitizedBody.resources);

    const userId = (req as any).user?.id;
    const user = (req as any).user;
    const organizationId = (req as any).user?.organization_id;
    const ipAddress = req.ip;
    const userAgent = req.get("User-Agent");

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: getTranslation(
          res,
          "UNAUTHORIZED_ACCESS",
          "Unauthorized access"
        ),
      });
    }

    // Check if user has permission to create recipes
    const hasPermission = await validateModulePermission(
      user,
      organizationId,
      MODULE_SLUGS.RECIPE,
      PERMISSION_TYPES.CREATE,
      getPlatformFromRequest(req)
    );

    if (!hasPermission) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: getTranslation(res, "PERMISSION_DENIED", "Permission denied"),
      });
    }

    // Generate unique slug from recipe title
    const checkSlugExists = async (slug: string): Promise<boolean> => {
      const existingRecipe = await Recipe.findOne({
        where: {
          recipe_slug: slug,
          organization_id: organizationId,
          recipe_status: {
            [Op.not]: RecipeStatus.deleted,
          },
        },
        transaction, // Use transaction to prevent race conditions
        lock: true, // Add row-level lock for consistency
      });
      return !!existingRecipe;
    };

    // Generate unique slug
    const recipe_slug = await generateUniqueSlug(
      recipe_title,
      checkSlugExists,
      {
        maxLength: 25,
        separator: "-",
        lowercase: true,
      }
    );

    // Create main recipe with initial timestamps
    const currentTimestamp = new Date();
    const recipeData = {
      recipe_title,
      recipe_public_title,
      recipe_description,
      recipe_preparation_time,
      recipe_cook_time,
      has_recipe_public_visibility: has_recipe_public_visibility || false,
      has_recipe_private_visibility: has_recipe_private_visibility || false,
      recipe_status: recipe_status || RecipeStatus.draft,
      recipe_serve_in,
      recipe_complexity_level,
      recipe_garnish,
      recipe_head_chef_tips,
      recipe_foh_tips,
      recipe_impression,
      recipe_yield,
      recipe_yield_unit,
      recipe_total_portions,
      recipe_single_portion_size,
      recipe_serving_method,
      recipe_placeholder,
      recipe_slug,
      ingredient_costs_updated_at: currentTimestamp,
      nutrition_values_updated_at: currentTimestamp,
      vitamin_a: vitamin_a ? parseFloat(vitamin_a) : null,
      vitamin_c: vitamin_c ? parseFloat(vitamin_c) : null,
      calcium: calcium ? parseFloat(calcium) : null,
      iron: iron ? parseFloat(iron) : null,
      is_ingredient_cooking_method:
        is_ingredient_cooking_method === "true" ||
        is_ingredient_cooking_method === true,
      is_preparation_method:
        is_preparation_method === "true" || is_preparation_method === true,
      is_cost_manual: is_cost_manual === "true" || is_cost_manual === true,
      organization_id: organizationId,
      created_by: userId,
      updated_by: userId,
    };

    const newRecipe = await Recipe.create(recipeData, { transaction });

    // Create recipe categories (optional for draft)
    if (categories && categories.length > 0) {
      const categoryData = categories.map((categoryId: number) => ({
        recipe_id: newRecipe.id,
        category_id: categoryId,
        status: RecipeCategoryStatus.active,
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      }));
      await RecipeCategory.bulkCreate(categoryData, { transaction });
    }

    // Create recipe attributes (optional for draft) - Type-wise handling
    const allAttributeData: any[] = [];

    // Handle nutrition attributes (with units)
    if (nutrition_attributes && nutrition_attributes.length > 0) {
      const nutritionData = nutrition_attributes.map((attr: any) => ({
        recipe_id: newRecipe.id,
        attributes_id: attr.id,
        unit_of_measure: attr.unit_of_measure,
        unit: attr.unit,
        status: RecipeAttributesStatus.active,
        organization_id: organizationId,
        attribute_description: attr.description,
        created_by: userId,
        updated_by: userId,
      }));
      allAttributeData.push(...nutritionData);
    }

    // Handle allergen attributes (with may_contain support)
    if (allergen_attributes) {
      // Check if it's the new format with contains/may_contain
      if (
        typeof allergen_attributes === "object" &&
        !Array.isArray(allergen_attributes)
      ) {
        const { contains, may_contain } = allergen_attributes;

        // Validate: ensure no allergen is in both contains and may_contain
        const containsIds = contains || [];
        const mayContainIds = may_contain || [];
        const duplicates = containsIds.filter((id: number) =>
          mayContainIds.includes(id)
        );

        if (duplicates.length > 0) {
          await transaction.rollback();
          return res.status(StatusCodes.BAD_REQUEST).json({
            status: false,
            message: `Allergen IDs ${duplicates.join(", ")} cannot be in both 'contains' and 'may_contain' lists`,
            field: "allergen_attributes",
            duplicates: duplicates,
          });
        }

        // Add main allergens (contains)
        if (containsIds.length > 0) {
          const containsData = containsIds.map((attrId: number) => ({
            recipe_id: newRecipe.id,
            attributes_id: attrId,
            unit_of_measure: null,
            unit: null,
            status: RecipeAttributesStatus.active,
            organization_id: organizationId,
            attribute_description: null,
            may_contain: false,
            created_by: userId,
            updated_by: userId,
          }));
          allAttributeData.push(...containsData);
        }

        // Add may_contain allergens
        if (mayContainIds.length > 0) {
          const mayContainData = mayContainIds.map((attrId: number) => ({
            recipe_id: newRecipe.id,
            attributes_id: attrId,
            unit_of_measure: null,
            unit: null,
            status: RecipeAttributesStatus.active,
            organization_id: organizationId,
            attribute_description: null,
            may_contain: true,
            created_by: userId,
            updated_by: userId,
          }));
          allAttributeData.push(...mayContainData);
        }
      } else if (Array.isArray(allergen_attributes)) {
        // Legacy format: simple array of IDs (treat as contains)
        const allergenData = allergen_attributes.map((attrId: number) => ({
          recipe_id: newRecipe.id,
          attributes_id: attrId,
          unit_of_measure: null,
          unit: null,
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          attribute_description: null,
          may_contain: false,
          created_by: userId,
          updated_by: userId,
        }));
        allAttributeData.push(...allergenData);
      }
    }

    // Handle cuisine attributes (simple IDs)
    if (cuisine_attributes && cuisine_attributes.length > 0) {
      const cuisineData = cuisine_attributes.map((attrId: number) => ({
        recipe_id: newRecipe.id,
        attributes_id: attrId,
        unit_of_measure: null,
        unit: null,
        status: RecipeAttributesStatus.active,
        organization_id: organizationId,
        attribute_description: null,
        created_by: userId,
        updated_by: userId,
      }));
      allAttributeData.push(...cuisineData);
    }

    // Handle dietary attributes (simple IDs)
    if (dietary_attributes && dietary_attributes.length > 0) {
      const dietaryData = dietary_attributes.map((attrId: number) => ({
        recipe_id: newRecipe.id,
        attributes_id: attrId,
        unit_of_measure: null,
        unit: null,
        status: RecipeAttributesStatus.active,
        organization_id: organizationId,
        attribute_description: null,
        created_by: userId,
        updated_by: userId,
      }));
      allAttributeData.push(...dietaryData);
    }

    // Handle HACCP attributes (with description and use_default)
    if (haccp_attributes && haccp_attributes.length > 0) {
      const haccpData = haccp_attributes.map((attr: any) => ({
        recipe_id: newRecipe.id,
        attributes_id: attr.id,
        unit_of_measure: null,
        unit: null,
        status: RecipeAttributesStatus.active,
        organization_id: organizationId,
        attribute_description: attr.description,
        use_default: attr.use_default || false,
        created_by: userId,
        updated_by: userId,
      }));
      allAttributeData.push(...haccpData);
    }

    // Legacy support: Handle single attributes array
    if (attributes && attributes.length > 0) {
      const legacyData = attributes.map((attr: any) => ({
        recipe_id: newRecipe.id,
        attributes_id: attr.id,
        unit_of_measure: attr.unit_of_measure,
        unit: attr.unit,
        status: RecipeAttributesStatus.active,
        organization_id: organizationId,
        attribute_description: attr.description,
        created_by: userId,
        updated_by: userId,
      }));
      allAttributeData.push(...legacyData);
    }

    // Bulk create all attributes if any exist
    if (allAttributeData.length > 0) {
      await RecipeAttributes.bulkCreate(allAttributeData, { transaction });
    }

    // Create recipe ingredients (optional for draft)
    if (ingredients && ingredients.length > 0) {
      const ingredientData = ingredients.map((ing: any) => ({
        recipe_id: newRecipe.id,
        ingredient_id: ing.id,
        recipe_ingredient_status: RecipeIngredientsStatus.active,
        ingredient_quantity: ing.quantity,
        ingredient_measure: ing.measure,
        ingredient_wastage: ing.wastage,
        ingredient_cost: ing.cost,
        ingredient_cooking_method: ing.cooking_method,
        preparation_method: ing.preparation_method,
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      }));
      await RecipeIngredients.bulkCreate(ingredientData, { transaction });
    }

    // Create recipe resources (optional for draft)
    if (resources && resources.length > 0) {
      const resourceData = resources.map((resource: any) => ({
        recipe_id: newRecipe.id,
        type: resource.type,
        item_id: resource.item_id,
        item_link: resource.item_link,
        item_link_type: resource.item_link_type,
        status: RecipeResourceStatus.active,
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      }));
      await RecipeResources.bulkCreate(resourceData, { transaction });
    }

    // Handle file uploads FIRST (before creating steps)
    const uploadedFileResources: any[] = [];
    let updatedPlaceholder = recipe_placeholder; // Default to form value

    if (
      req.files &&
      typeof req.files === "object" &&
      !Array.isArray(req.files)
    ) {
      const files = req.files as { [fieldname: string]: any[] };

      // Handle recipe placeholder upload
      if (files.recipePlaceholder && files.recipePlaceholder.length > 0) {
        const placeholderFile = files.recipePlaceholder[0];

        if (placeholderFile.isMovable) {
          // Move file to correct location
          const orgName = organizationId ? organizationId.toString() : null;
          const destinationPath =
            RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_IMAGE.destinationPath(
              orgName,
              newRecipe.id,
              placeholderFile.filename
            );

          const bucketName = process.env.NODE_ENV || "development";

          // Track file operation for potential rollback
          fileTracker.trackMove(placeholderFile.path, destinationPath);

          const moveResult = await uploadService.moveFileInBucket(
            bucketName,
            placeholderFile.path,
            destinationPath,
            placeholderFile.item_id
          );

          if (moveResult.success) {
            updatedPlaceholder = placeholderFile.item_id;
            // Update the recipe with the new placeholder item_id
            await Recipe.update(
              { recipe_placeholder: updatedPlaceholder },
              { where: { id: newRecipe.id }, transaction }
            );
          } else {
            throw new Error(
              `Failed to move placeholder file: ${moveResult.error}`
            );
          }
        } else {
          // File already exists, use its item_id
          updatedPlaceholder = placeholderFile.item_id;
          // Update the recipe with the placeholder item_id
          await Recipe.update(
            { recipe_placeholder: updatedPlaceholder },
            { where: { id: newRecipe.id }, transaction }
          );
        }
      }

      // Handle recipe files (max 10) - these go to resources table
      if (files.recipeFiles && files.recipeFiles.length > 0) {
        for (const file of files.recipeFiles) {
          if (file.isMovable) {
            // Move file to correct location
            const orgName = organizationId ? organizationId.toString() : null;
            const destinationPath =
              RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_DOCUMENT.destinationPath(
                orgName,
                newRecipe.id,
                file.filename
              );

            const bucketName = process.env.NODE_ENV || "development";

            // Track file operation for potential rollback
            fileTracker.trackMove(file.path, destinationPath);

            const moveResult = await uploadService.moveFileInBucket(
              bucketName,
              file.path,
              destinationPath,
              file.item_id
            );

            if (moveResult.success) {
              // Add resource entry for newly uploaded files
              uploadedFileResources.push({
                recipe_id: newRecipe.id,
                type: "item",
                item_id: file.item_id,
                item_link: null,
                item_link_type: file.mimetype?.includes("audio")
                  ? "audio"
                  : file.mimetype?.includes("video")
                    ? "video"
                    : file.mimetype?.includes("pdf")
                      ? "pdf"
                      : file.mimetype?.includes("image")
                        ? "image"
                        : "document",
                status: RecipeResourceStatus.active,
                organization_id: organizationId,
                created_by: userId,
                updated_by: userId,
              });
            } else {
              throw new Error(
                `Failed to move recipe file: ${moveResult.error}`
              );
            }
          } else {
            // File already exists, just create resource reference
            uploadedFileResources.push({
              recipe_id: newRecipe.id,
              type: "item",
              item_id: file.item_id,
              item_link: null,
              item_link_type: file.mimetype?.includes("audio")
                ? "audio"
                : file.mimetype?.includes("video")
                  ? "video"
                  : file.mimetype?.includes("pdf")
                    ? "pdf"
                    : file.mimetype?.includes("image")
                      ? "image"
                      : "document",
              status: RecipeResourceStatus.active,
              organization_id: organizationId,
              created_by: userId,
              updated_by: userId,
            });
          }
        }
      }

      // Handle step images (dynamic based on step order) - these go to step table item_id
      if (
        files.stepImages &&
        files.stepImages.length > 0 &&
        steps &&
        steps.length > 0
      ) {
        // Sort steps by order to match with uploaded images
        const sortedSteps = [...steps].sort(
          (a: any, b: any) => a.order - b.order
        );

        for (
          let i = 0;
          i < Math.min(files.stepImages.length, sortedSteps.length);
          i++
        ) {
          const file = files.stepImages[i];
          const step = sortedSteps[i];
          const bucketName = process.env.NODE_ENV || "development";

          if (file.isMovable) {
            // Move file to correct location
            const orgName = organizationId ? organizationId.toString() : null;
            const destinationPath =
              RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_INSTRUCTION_MEDIA.destinationPath(
                orgName,
                newRecipe.id,
                step.order,
                file.filename
              );
            // Track file operation for potential rollback
            fileTracker.trackMove(file.path, destinationPath);

            const moveResult = await uploadService.moveFileInBucket(
              bucketName,
              file.path,
              destinationPath,
              file.item_id
            );

            if (moveResult.success) {
              // Update the step with the image item_id (NOT adding to resources)
              step.item_id = file.item_id;
            } else {
              throw new Error(`Failed to move step image: ${moveResult.error}`);
            }
          } else {
            // File already exists, just update step with item_id
            step.item_id = file.item_id;
          }
        }
      }

      // Create resource records only for recipe files (not step images)
      if (uploadedFileResources.length > 0) {
        await RecipeResources.bulkCreate(uploadedFileResources, {
          transaction,
        });
      }
    }

    // Create recipe steps AFTER file upload processing (optional for draft)
    if (steps && steps.length > 0) {
      const stepData = steps.map((step: any) => ({
        recipe_id: newRecipe.id,
        item_id: step.item_id || null, // Now includes uploaded image item_id
        recipe_step_order: step.order,
        recipe_step_description: step.description,
        status: RecipeStepsStatus.active,
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      }));
      await RecipeSteps.bulkCreate(stepData, { transaction });
    }

    // Create history record
    await createRecipeHistory(
      {
        recipe_id: newRecipe.id,
        action: RecipeHistoryAction.created,
        field_name: "recipe_created",
        description: `Recipe "${recipe_title}" created`,
        ip_address: ipAddress,
        user_agent: userAgent,
        organization_id: organizationId,
        created_by: userId,
      },
      transaction
    );

    // Commit transaction before fetching complete recipe data
    await transactionManager.commit();

    // Clear file operations after successful commit
    fileTracker.clear();

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: getTranslation(
        res,
        "RECIPE_CREATED_SUCCESSFULLY",
        "Recipe created successfully"
      ),
    });
  } catch (error: unknown) {
    // Enhanced error handling with file rollback
    await transactionManager.rollback();
    await fileTracker.rollback();

    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error creating recipe"
    );
  }
};

/**
 * @description Get single recipe by ID or slug using raw queries for better performance
 * @route GET /api/v1/recipes/get-by-id/:identifier
 * @access Private
 */
const getRecipeById = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const organizationId = (req as any).user?.organization_id;

    // Validate recipe identifier
    if (!id) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("INVALID_RECIPE_IDENTIFIER"),
      });
    }

    // Determine if identifier is numeric (ID) or string (slug)
    const isNumeric = !isNaN(Number(id));
    let recipeData;

    if (isNumeric) {
      // Get recipe by ID using existing raw query function
      recipeData = await getRecipeByIdRaw(Number(id), organizationId);
    } else {
      // Get recipe by slug using new helper function
      recipeData = await getRecipeBySlugRaw(id, organizationId);
    }

    if (!recipeData) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("RECIPE_NOT_FOUND"),
      });
    }

    // Add highlight information to the recipe data
    const recipeId = isNumeric ? Number(id) : recipeData.id;
    const highlightResult = await getRecipeHighlight(recipeId, organizationId);
    const highlight = highlightResult ? highlightResult.highlight : {};
    const hasRecent = await hasRecentChanges(recipeId, organizationId);

    // Get organization settings to check if highlights are allowed
    const organizationSettings =
      await settingsService.getStructuredSettingsByOrganizationId(
        organizationId
      );
    const isHighlightedAllowed =
      organizationSettings.privateRecipeVisibilitySettings.highlightChanges;

    // Enhance recipe data with highlight information
    const enhancedRecipeData = {
      ...recipeData,
    };

    if (isHighlightedAllowed) {
      enhancedRecipeData.highlight = highlight;
      enhancedRecipeData.hasRecentChanges = hasRecent;
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("RECIPE_FETCHED_SUCCESSFULLY"),
      data: enhancedRecipeData,
    });
  } catch (error: unknown) {
    const customError = error as CustomError;
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_FETCHING_RECIPE"),
      error:
        customError.message || "Unknown error occurred while fetching recipe",
    });
  }
};

/**
 * @description Get single public recipe by ID or slug with organization settings
 * @route GET /api/v1/public/recipes/get-by-id/:identifier
 * @access Public
 */
const getPublicRecipeById = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const { id } = req.params;

    // Validate recipe identifier
    if (!id) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("INVALID_RECIPE_IDENTIFIER"),
      });
    }

    // Determine if identifier is numeric (ID) or string (slug)
    const isNumeric = !isNaN(Number(id));
    let recipeData;

    if (isNumeric) {
      // Get recipe by ID using PUBLIC-specific function that includes settings
      // For public API, we don't pass organizationId to ensure only public recipes are returned
      recipeData = await getPublicRecipeByIdRaw(Number(id));
    } else {
      // Get recipe by slug using PUBLIC-specific function that includes settings
      // For public API, we don't pass organizationId to ensure only public recipes are returned
      recipeData = await getPublicRecipeBySlugRaw(id);
    }

    if (!recipeData) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("RECIPE_NOT_FOUND"),
      });
    }

    const organizationSettings = recipeData.organization_settings; // Get organization settings from recipe data (already included by getPublicRecipeByIdRaw/getPublicRecipeBySlugRaw)
    const isHighlightedAllowed =
      organizationSettings.privateRecipeVisibilitySettings.highlightChanges;
    const isRecipeaPublicAccessAllowed =
      organizationSettings.publicRecipeSettings.publicStoreAccess; // Changed from publicRecipeVisibility to publicStoreAccess to match the settings table column name

    // Check if recipe is public and published and public access is allowed (publicStoreAccess)
    if (
      !recipeData.has_recipe_public_visibility ||
      recipeData.recipe_status !== RecipeStatus.publish ||
      !isRecipeaPublicAccessAllowed
    ) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("RECIPE_NOT_FOUND"),
      });
    }

    // Add highlight information to the recipe data (for public recipes, we don't pass organizationId)
    const recipeId = isNumeric ? Number(id) : recipeData.id;
    const highlightResult = await getRecipeHighlight(
      recipeId,
      recipeData.organization_id
    );
    const highlight = highlightResult ? highlightResult.highlight : {};
    const hasRecent = await hasRecentChanges(recipeId, null);

    // Filter recipe data based on allowed public fields
    const filteredRecipeData = filterPublicRecipeFields(
      recipeData,
      organizationSettings
    ); // Pass organizationSettings to filter recipe data based on allowed public fields

    const enhancedRecipeData: any = {
      ...filteredRecipeData,
    };
    if (isHighlightedAllowed) {
      enhancedRecipeData.highlight = highlight;
      enhancedRecipeData.hasRecentChanges = hasRecent;
    }

    // Settings are already included by the getPublicRecipeByIdRaw/getPublicRecipeBySlugRaw functions
    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("RECIPE_FETCHED_SUCCESSFULLY"),
      data: enhancedRecipeData,
      organization_details: {
        name: await getOrgName(recipeData.organization_id),
        logo: await getOrganizationLogo(recipeData.organization_id),
        contact_number: await getOrganizationContactInfo(
          recipeData.organization_id
        ),
      },
    });
  } catch (error: unknown) {
    const customError = error as CustomError;
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_FETCHING_RECIPE"),
      error:
        customError.message || "Unknown error occurred while fetching recipe",
    });
  }
};

/**
 * @description Ultra-optimized public recipes list with millisecond response times
 * @route GET /api/v1/public/recipes/list
 * @access Public
 */
const getPublicRecipesList = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    // For public endpoint: Only public + published recipes
    // No role, organization, or assignment restrictions
    const filters = { ...req.query };
    filters.visibility = "public"; // Force public visibility
    filters.recipe_status = RecipeStatus.publish; // Only published recipes

    // Remove any organization or assignment filters for public API, if organization_slug is provided in the query params. Otherwise, return empty data. If organization_slug is provided, check if the organization has public access enabled. If not, return empty data. Otherwise, set organization_id filter.
    if (filters.organization_slug) {
      const findOrgId = await Settings.findOne({
        attributes: ["organization_id"],
        where: {
          setting_category: SettingCategory.RECIPE,
          setting_key: "recipe.organization_name",
          setting_value: filters.organization_slug,
        },
        raw: true,
      });
      if (findOrgId && findOrgId.organization_id) {
        const organizationSettings =
          await settingsService.getStructuredSettingsByOrganizationId(
            findOrgId.organization_id
          );
        const isRecipeaPublicAccessAllowed =
          organizationSettings.publicRecipeSettings.publicStoreAccess; // Changed from publicRecipeVisibility to publicStoreAccess to match the settings table column name

        if (!isRecipeaPublicAccessAllowed) {
          return res.status(StatusCodes.NOT_FOUND).json({
            status: false,
            message: "Public recipes retrieved successfully",
            count: 0,
            data: [],
            page: 1,
            size: 0,
            total_pages: 0,
            organization_details: {
              name: await getOrgName(findOrgId.organization_id),
              logo: await getOrganizationLogo(findOrgId.organization_id),
              contact_number: await getOrganizationContactInfo(findOrgId.organization_id)
            },
          });
        } else {
          filters.organization_id = findOrgId.organization_id;
        }
        delete filters.organization_slug;
      } else {
        return res.status(StatusCodes.NOT_FOUND).json({
          status: false,
          message: "Public recipes retrieved successfully",
          count: 0,
          data: [],
          page: 1,
          size: 0,
          total_pages: 0,
          organization_details: {
            name: "",
            logo: "",
            contact_number: "",
          }
        });
      }
    } else {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: "Public recipes retrieved successfully",
        count: 0,
        data: [],
        page: 1,
        size: 0,
        total_pages: 0,
        organization_details: {
          name: "",
          logo: "",
          contact_number: "",
        }
      });
    }

    delete filters.assigned_user_id;
    const result = await getRecipesListRaw(filters);
    return res.status(StatusCodes.OK).json(result);
  } catch (error: unknown) {
    const customError = error as CustomError;
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_FETCHING_RECIPES"),
      error: customError.message,
    });
  }
};

/**
 * @description Ultra-optimized private recipes list with millisecond response times
 * @route GET /api/v1/private/recipes/list
 * @access Private
 */
const getPrivateRecipesList = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;
    const userRole = (req as any).user?.roles;

    // Check user role permission
    let findRole: any;

    if (req.headers["platform-type"] === "web") {
      const activeRoleId =
        (req as any).user?.user_role_id ??
        (req as any).user?.user_active_role_id;
      findRole = userRole.find((role: any) => role.id == activeRoleId);
    } else if (
      req.headers["platform-type"] === "ios" ||
      req.headers["platform-type"] === "android"
    ) {
      findRole = userRole.find(
        (role: any) => role.id == (req as any).user?.user_active_role_id
      );
    } else {
      findRole = userRole.find(
        (role: any) => role.id == (req as any).user?.user_active_role_id
      );
    }

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: getTranslation(
          res,
          "UNAUTHORIZED_ACCESS",
          "Unauthorized access"
        ),
      });
    }

    // Add organization filter and set visibility based on query parameter
    const filters = { ...req.query };
    if (organizationId) {
      filters.organization_id = organizationId;
    }
    // Role-based filtering logic
    if (findRole && canViewAllPrivateRecipes(findRole)) {
      // SUPER_ADMIN, ADMIN, DIRECTOR, HR: Can see all recipes with visibility filter
      // Support visibility filter: public, private, all
      if (filters.visibility == "all") {
        delete filters.visibility;
      }
    } else {
      // ALL OTHER USERS (including other admin-side users): Only assigned recipes + must be private + must be published
      filters.assigned_user_id = userId; // Filter by mo_recipe_user table
      filters.visibility = "private"; // Force private visibility for normal users
      filters.recipe_status = RecipeStatus.publish; // Only show published recipes for normal users
    }

    const result = await getRecipesListRaw(filters, userId, organizationId);

    // Add highlight information to private recipes list
    if (
      result.status &&
      result.data &&
      Array.isArray(result.data) &&
      result.data.length > 0
    ) {
      try {
        // Get organization settings to check if highlights are allowed
        const organizationSettings =
          await settingsService.getStructuredSettingsByOrganizationId(
            organizationId
          );
        const isHighlightedAllowed =
          organizationSettings.privateRecipeVisibilitySettings.highlightChanges;

        if (isHighlightedAllowed) {
          // Get recipe IDs for bulk highlight query
          const recipeIds = result.data.map((recipe: any) => recipe.id);
          const bulkHighlights = await getBulkRecipeHighlights(
            recipeIds,
            organizationId
          );

          // Add highlight information to each recipe
          result.data = result.data.map((recipe: any) => {
            const recipeHighlight =
              (bulkHighlights.highlights as any)[recipe.id] || null;
            return {
              ...recipe,
              highlight: recipeHighlight,
              hasRecentChanges: !!recipeHighlight,
            };
          });

          // Add highlight metadata to response
          result.highlight_metadata = bulkHighlights.metadata;
        }
      } catch (error) {
        console.error(
          "Error adding highlights to private recipes list:",
          error
        );
        // Continue without highlights if there's an error
      }
    }

    return res.status(StatusCodes.OK).json(result);
  } catch (error: unknown) {
    const customError = error as CustomError;
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_FETCHING_RECIPES"),
      error: customError.message,
    });
  }
};

/**
 * @description Handle recipe export functionality
 */
const handleRecipeExport = async (
  filters: any,
  recipes: any[],
  format: string,
  res: Response,
  req?: Request
): Promise<any> => {
  try {
    const timestamp = new Date().toISOString().split("T")[0];

    if (format === "excel") {
      const workbook = await exportRecipesToExcel(recipes, filters);
      const filename = `recipes_export_${timestamp}.xlsx`;

      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${filename}"`
      );

      return workbook.xlsx.write(res).then(() => {
        res.end();
      });
    } else if (format === "csv") {
      const csvData = exportRecipesToCSV(recipes);
      const filename = `recipes_export_${timestamp}.csv`;

      res.setHeader("Content-Type", "text/csv");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${filename}"`
      );

      return res.send(csvData);
    } else if (format === "pdf") {
      // PDF export is only supported for single recipe
      if (recipes.length !== 1) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("PDF_EXPORT_SINGLE_RECIPE_ONLY"),
        });
      }
      const recipe = recipes[0];
      // Generate professional PDF with all images and content
      const pdfBuffer = await exportRecipeToPDF(recipe);
      const filename = `recipe_${recipe.recipe_slug || recipe.id}_${timestamp}.pdf`;

      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${filename}"`
      );

      return res.send(pdfBuffer);
    }
  } catch (error) {
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_EXPORTING_RECIPES"),
      error: (error as Error).message,
    });
  }
};

/**
 * @description Archive Recipe - Archives recipe and removes all bookmarks and assignments
 * @route PUT /api/v1/recipes/archive/:id
 * @access Private
 */
const archiveRecipe = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    const transaction = await transactionManager.start();
    const { id } = req.params;
    const userId = (req as any).user?.id;
    const user = (req as any).user;
    const organizationId = (req as any).user?.organization_id;
    const userRole = (req as any).user?.roles?.[0];
    const ipAddress = req.ip || "Unknown";
    const userAgent = req.get("User-Agent") || "Unknown";

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: getTranslation(
          res,
          "UNAUTHORIZED_ACCESS",
          "Unauthorized access"
        ),
      });
    }

    // Check if user has permission to create recipes
    const hasPermission = await validateModulePermission(
      user,
      organizationId,
      MODULE_SLUGS.RECIPE,
      PERMISSION_TYPES.CREATE,
      getPlatformFromRequest(req)
    );

    if (!hasPermission) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: getTranslation(res, "PERMISSION_DENIED", "Permission denied"),
      });
    }

    // Find existing recipe
    const existingRecipe = await Recipe.findOne({
      where: {
        id,
        organization_id: organizationId,
        recipe_status: {
          [Op.not]: RecipeStatus.deleted,
        },
      },
    });

    if (!existingRecipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: getTranslation(res, "RECIPE_NOT_FOUND", "Recipe not found"),
      });
    }

    // Check if recipe is already archived
    if (existingRecipe.recipe_status === RecipeStatus.archived) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: getTranslation(
          res,
          "RECIPE_ALREADY_ARCHIVED",
          "Recipe is already archived"
        ),
      });
    }

    // 1. Archive the recipe
    await Recipe.update(
      {
        recipe_status: RecipeStatus.archived,
        updated_by: userId,
      },
      {
        where: { id },
        transaction,
      }
    );

    // 2. Remove all bookmarks (set status to inactive)
    await RecipeUser.update(
      {
        status: RecipeUserStatus.inactive,
        updated_by: userId,
      },
      {
        where: { recipe_id: id },
        transaction,
      }
    );

    // 3. Create detailed history record
    await createRecipeHistory(
      {
        recipe_id: Number(id),
        action: RecipeHistoryAction.archived,
        field_name: "recipe_status",
        old_value: existingRecipe.recipe_status,
        new_value: RecipeStatus.archived,
        description: `Recipe "${existingRecipe.recipe_title}" was archived.\nAll user assignments removed.\nAll bookmarks removed.`,
        ip_address: ipAddress,
        user_agent: userAgent,
        organization_id: organizationId,
        created_by: userId,
      },
      transaction
    );

    await transactionManager.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: getTranslation(
        res,
        "RECIPE_ARCHIVED_SUCCESSFULLY",
        "Recipe archived successfully and all assignments/bookmarks removed"
      ),
    });
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error archiving recipe"
    );
  }
};

/**
 * @description Delete Recipe - Deletes recipe with specific conditions
 * @route DELETE /api/v1/recipes/delete/:id
 * @access Private
 * @description Deletes recipe only if: 1) No users assigned OR 2) Recipe is archived
 */
const deleteRecipe = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    const transaction = await transactionManager.start();
    const { id } = req.params;
    const userId = (req as any).user?.id;
    const user = (req as any).user;
    const organizationId = (req as any).user?.organization_id;
    const ipAddress = req.ip || "Unknown";
    const userAgent = req.get("User-Agent") || "Unknown";

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: getTranslation(
          res,
          "UNAUTHORIZED_ACCESS",
          "Unauthorized access"
        ),
      });
    }

    // Check if user has permission to create recipes
    const hasPermission = await validateModulePermission(
      user,
      organizationId,
      MODULE_SLUGS.RECIPE,
      PERMISSION_TYPES.DELETE,
      getPlatformFromRequest(req)
    );

    if (!hasPermission) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: getTranslation(res, "PERMISSION_DENIED", "Permission denied"),
      });
    }

    // Find existing recipe
    const existingRecipe = await Recipe.findOne({
      where: {
        id,
        organization_id: organizationId,
        recipe_status: {
          [Op.not]: RecipeStatus.deleted,
        },
      },
    });

    if (!existingRecipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: getTranslation(res, "RECIPE_NOT_FOUND", "Recipe not found"),
      });
    }

    // Check if recipe has active assignments (unless it's archived)
    if (existingRecipe.recipe_status !== RecipeStatus.archived) {
      const activeAssignments = await RecipeUser.count({
        where: {
          recipe_id: id,
          status: RecipeUserStatus.active,
        },
      });

      if (activeAssignments > 0) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: getTranslation(
            res,
            "RECIPE_HAS_ACTIVE_ASSIGNMENTS",
            "Cannot delete recipe with active user assignments. Please archive the recipe first or remove all assignments."
          ),
          active_assignments: activeAssignments,
        });
      }
    }

    // Delete the recipe (soft delete)
    await Recipe.update(
      {
        recipe_status: RecipeStatus.deleted,
        updated_by: userId,
      },
      {
        where: { id },
        transaction,
      }
    );

    // Create history record
    await createRecipeHistory(
      {
        recipe_id: Number(id),
        action: RecipeHistoryAction.deleted,
        field_name: "recipe_status",
        old_value: existingRecipe.recipe_status,
        new_value: RecipeStatus.deleted,
        description: `Recipe "${existingRecipe.recipe_title}" deleted`,
        ip_address: ipAddress,
        user_agent: userAgent,
        organization_id: organizationId,
        created_by: userId,
      },
      transaction
    );

    await transactionManager.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: getTranslation(
        res,
        "RECIPE_DELETED_SUCCESSFULLY",
        "Recipe deleted successfully"
      ),
    });
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error deleting recipe"
    );
  }
};

/**
 * @description Update Recipe (Smart Update) - Intelligently updates only provided fields
 * @route PUT /api/v1/recipes/update/:id
 * @access Private
 * @description Supports both full and section-wise updates. Only updates provided fields.
 */
const updateRecipe = async (req: Request, res: Response): Promise<any> => {
  const transactionManager = new TransactionManager();
  const fileTracker = new FileOperationTracker();

  try {
    const transaction = await transactionManager.start();
    const { id } = req.params;

    // Helper function to parse JSON fields (same as create)
    const parseJsonField = (field: any) => {
      if (typeof field === "string") {
        try {
          return JSON.parse(field);
        } catch {
          return field;
        }
      }
      return field;
    };

    // Parse JSON arrays from form data
    const categories = parseJsonField(req.body.categories);
    const attributes = parseJsonField(req.body.attributes); // Legacy support
    const nutrition_attributes = parseJsonField(req.body.nutrition_attributes);
    const allergen_attributes = parseJsonField(req.body.allergen_attributes);
    const cuisine_attributes = parseJsonField(req.body.cuisine_attributes);
    const dietary_attributes = parseJsonField(req.body.dietary_attributes);
    const haccp_attributes = parseJsonField(req.body.haccp_attributes);
    const ingredients = parseJsonField(req.body.ingredients);
    const steps = parseJsonField(req.body.steps);
    const resources = parseJsonField(req.body.resources);

    // Extract other form fields
    const {
      recipe_title,
      recipe_public_title,
      recipe_description,
      recipe_preparation_time,
      recipe_cook_time,
      has_recipe_public_visibility,
      has_recipe_private_visibility,
      recipe_status,
      recipe_serve_in,
      recipe_complexity_level,
      recipe_garnish,
      recipe_head_chef_tips,
      recipe_foh_tips,
      recipe_impression,
      recipe_yield,
      recipe_yield_unit,
      recipe_total_portions,
      recipe_single_portion_size,
      recipe_serving_method,
      recipe_placeholder,
      vitamin_a,
      vitamin_c,
      calcium,
      iron,
      is_ingredient_cooking_method,
      is_preparation_method,
      is_cost_manual,
    } = req.body;

    const updateData: any = {
      recipe_title,
      recipe_public_title,
      recipe_description,
      recipe_preparation_time,
      recipe_cook_time,
      has_recipe_public_visibility,
      has_recipe_private_visibility,
      recipe_status,
      recipe_serve_in,
      recipe_complexity_level,
      recipe_garnish,
      recipe_head_chef_tips,
      recipe_foh_tips,
      recipe_impression,
      recipe_yield,
      recipe_yield_unit,
      recipe_total_portions,
      recipe_single_portion_size,
      recipe_serving_method,
      recipe_placeholder,
      vitamin_a: vitamin_a ? parseFloat(vitamin_a) : null,
      vitamin_c: vitamin_c ? parseFloat(vitamin_c) : null,
      calcium: calcium ? parseFloat(calcium) : null,
      iron: iron ? parseFloat(iron) : null,
      is_ingredient_cooking_method:
        is_ingredient_cooking_method === "true" ||
        is_ingredient_cooking_method === true,
      is_preparation_method:
        is_preparation_method === "true" || is_preparation_method === true,
      is_cost_manual: is_cost_manual === "true" || is_cost_manual === true,
      categories,
      attributes, // Legacy support
      nutrition_attributes,
      allergen_attributes,
      cuisine_attributes,
      dietary_attributes,
      haccp_attributes,
      ingredients,
      steps,
      resources,
    };

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;
    const user = (req as any).user;
    const ipAddress = req.ip || "Unknown";
    const userAgent = req.get("User-Agent") || "Unknown";

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS"),
      });
    }

    // Check if user has permission to create recipes
    const hasPermission = await validateModulePermission(
      user,
      organizationId,
      MODULE_SLUGS.RECIPE,
      PERMISSION_TYPES.EDIT,
      getPlatformFromRequest(req)
    );

    if (!hasPermission) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: getTranslation(res, "PERMISSION_DENIED", "Permission denied"),
      });
    }

    // Find existing recipe
    const existingRecipe = await Recipe.findOne({
      where: {
        id,
        organization_id: organizationId,
      },
      ...getRecipeQueryOptions(),
    });

    if (!existingRecipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("RECIPE_NOT_FOUND"),
      });
    }

    // Store old values for history tracking
    const oldValues = { ...existingRecipe.toJSON() };

    // Fetch old ingredients, steps, resources
    const oldIngredients = await RecipeIngredients.findAll({
      where: {
        recipe_id: id,
        recipe_ingredient_status: RecipeIngredientsStatus.active,
      },
      transaction,
      raw: true,
    });
    const oldSteps = await RecipeSteps.findAll({
      where: { recipe_id: id, status: RecipeStepsStatus.active },
      transaction,
      raw: true,
    });
    const oldResources = await RecipeResources.findAll({
      where: { recipe_id: id, status: RecipeResourceStatus.active },
      transaction,
      raw: true,
    });
    oldValues.ingredients = oldIngredients;
    oldValues.steps = oldSteps;
    oldValues.resources = oldResources;

    // Initialize change tracking variables
    const allChanges: string[] = [];
    let hasAnyChanges = false;

    // Handle file uploads FIRST (same as create)
    const uploadedFileResources: any[] = [];
    let updatedPlaceholder = recipe_placeholder; // Default to form value

    // Smart placeholder handling
    if (recipe_placeholder === null) {
      // Explicitly set to null - remove placeholder
      updatedPlaceholder = null;
    } else if (recipe_placeholder === undefined) {
      // Not provided - keep existing
      updatedPlaceholder = existingRecipe.recipe_placeholder;
    }

    if (
      req.files &&
      typeof req.files === "object" &&
      !Array.isArray(req.files)
    ) {
      const files = req.files as { [fieldname: string]: any[] };

      // Handle recipe placeholder upload
      if (files.recipePlaceholder && files.recipePlaceholder.length > 0) {
        const placeholderFile = files.recipePlaceholder[0];

        if (placeholderFile.isMovable) {
          // Move file to correct location
          const orgName = organizationId ? organizationId.toString() : null;
          const destinationPath =
            RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_IMAGE.destinationPath(
              orgName,
              id,
              placeholderFile.filename
            );

          const bucketName = process.env.NODE_ENV || "development";

          // Track file operation for potential rollback
          fileTracker.trackMove(placeholderFile.path, destinationPath);

          const moveResult = await uploadService.moveFileInBucket(
            bucketName,
            placeholderFile.path,
            destinationPath,
            placeholderFile.item_id
          );

          if (moveResult.success) {
            updatedPlaceholder = placeholderFile.item_id;
            updateData.recipe_placeholder = updatedPlaceholder;
          } else {
            throw new Error(
              `Failed to move placeholder file: ${moveResult.error}`
            );
          }
        } else {
          // File already exists, use its item_id
          updatedPlaceholder = placeholderFile.item_id;
          updateData.recipe_placeholder = updatedPlaceholder;
        }
      }

      // Handle recipe files upload
      if (files.recipeFiles && files.recipeFiles.length > 0) {
        for (const file of files.recipeFiles) {
          if (file.isMovable) {
            // Move file to correct location
            const orgName = organizationId ? organizationId.toString() : null;
            const destinationPath =
              RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_DOCUMENT.destinationPath(
                orgName,
                id,
                file.filename
              );

            const bucketName = process.env.NODE_ENV || "development";

            // Track file operation for potential rollback
            fileTracker.trackMove(file.path, destinationPath);

            const moveResult = await uploadService.moveFileInBucket(
              bucketName,
              file.path,
              destinationPath,
              file.item_id
            );

            if (moveResult.success) {
              // Add resource entry for newly uploaded files
              uploadedFileResources.push({
                recipe_id: id,
                type: "item",
                item_id: file.item_id,
                item_link: null,
                item_link_type: file.mimetype?.includes("audio")
                  ? "audio"
                  : file.mimetype?.includes("video")
                    ? "video"
                    : file.mimetype?.includes("pdf")
                      ? "pdf"
                      : file.mimetype?.includes("image")
                        ? "image"
                        : "document",
                status: RecipeResourceStatus.active,
                organization_id: organizationId,
                created_by: userId,
                updated_by: userId,
              });
            } else {
              throw new Error(
                `Failed to move recipe file: ${moveResult.error}`
              );
            }
          } else {
            // File already exists, just create resource reference
            uploadedFileResources.push({
              recipe_id: id,
              type: "item",
              item_id: file.item_id,
              item_link: null,
              item_link_type: file.mimetype?.includes("audio")
                ? "audio"
                : file.mimetype?.includes("video")
                  ? "video"
                  : file.mimetype?.includes("pdf")
                    ? "pdf"
                    : file.mimetype?.includes("image")
                      ? "image"
                      : "document",
              status: RecipeResourceStatus.active,
              organization_id: organizationId,
              created_by: userId,
              updated_by: userId,
            });
          }
        }
      }

      // Handle step images upload
      if (files.stepImages && files.stepImages.length > 0) {
        const stepImageMap: { [order: number]: number } = {};

        for (let i = 0; i < files.stepImages.length; i++) {
          const file = files.stepImages[i];
          const stepOrder = i + 1; // Match with step order

          if (file.isMovable) {
            // Move file to correct location
            const orgName = organizationId ? organizationId.toString() : null;
            const destinationPath =
              RECIPE_FILE_UPLOAD_CONSTANT.RECIPE_INSTRUCTION_MEDIA.destinationPath(
                orgName,
                id,
                stepOrder,
                file.filename
              );

            const bucketName = process.env.NODE_ENV || "development";

            // Track file operation for potential rollback
            fileTracker.trackMove(file.path, destinationPath);

            const moveResult = await uploadService.moveFileInBucket(
              bucketName,
              file.path,
              destinationPath,
              file.item_id
            );

            if (moveResult.success) {
              stepImageMap[stepOrder] = file.item_id;
            } else {
              throw new Error(`Failed to move step image: ${moveResult.error}`);
            }
          } else {
            // File already exists, use its item_id
            stepImageMap[stepOrder] = file.item_id;
          }
        }

        // Update steps with image item_id
        if (updateData.steps && Array.isArray(updateData.steps)) {
          updateData.steps = updateData.steps.map((step: any) => ({
            ...step,
            item_id: stepImageMap[step.order] || step.item_id || null,
          }));
        }
      }

      // Merge uploaded file resources with existing resources, deduplicating by item_id
      if (uploadedFileResources.length > 0) {
        const existingResources = updateData.resources || [];
        // Create a map to deduplicate by item_id
        const resourceMap = new Map<number, any>();

        existingResources.forEach((res: any) => {
          if (res.item_id) {
            resourceMap.set(res.item_id, res);
          }
        });

        uploadedFileResources.forEach((res: any) => {
          if (res.item_id && !resourceMap.has(res.item_id)) {
            resourceMap.set(res.item_id, res);
          }
        });

        updateData.resources = Array.from(resourceMap.values());
      }
    }

    // Update recipe slug if title changed
    if (
      updateData.recipe_title &&
      updateData.recipe_title !== existingRecipe.recipe_title
    ) {
      const checkSlugExists = async (slug: string): Promise<boolean> => {
        const checkSlugRecipeExists = await Recipe.findOne({
          where: {
            recipe_slug: slug,
            organization_id: organizationId,
            id: { [Op.ne]: existingRecipe.id }, // Exclude current recipe
          },
          transaction, // Use transaction to prevent race conditions
          lock: true, // Add row-level lock for consistency
        });
        return !!checkSlugRecipeExists;
      };

      updateData.recipe_slug = await generateUniqueSlug(
        updateData.recipe_title,
        checkSlugExists,
        {
          maxLength: 25,
          separator: "-",
          lowercase: true,
        }
      );
    }

    // Update main recipe fields
    const recipeFields = [
      "recipe_title",
      "recipe_public_title",
      "recipe_description",
      "recipe_preparation_time",
      "recipe_cook_time",
      "has_recipe_public_visibility",
      "has_recipe_private_visibility",
      "recipe_status",
      "recipe_serve_in",
      "recipe_complexity_level",
      "recipe_garnish",
      "recipe_head_chef_tips",
      "recipe_foh_tips",
      "recipe_impression",
      "recipe_yield",
      "recipe_yield_unit",
      "recipe_total_portions",
      "recipe_single_portion_size",
      "recipe_serving_method",
      "recipe_placeholder",
      "recipe_slug",
      "vitamin_a",
      "vitamin_c",
      "calcium",
      "iron",
      "is_ingredient_cooking_method",
      "is_preparation_method",
      "is_cost_manual",
    ];

    const recipeUpdateData: any = { updated_by: userId };
    let hasChanges = false;

    recipeFields.forEach((field) => {
      if (updateData[field] !== undefined) {
        recipeUpdateData[field] = updateData[field];
        const oldValue = (existingRecipe as any)[field];
        const newValue = updateData[field];

        // Special handling for boolean fields to avoid type mismatch issues
        let valuesAreEqual = false;
        if (
          field === "is_preparation_method" ||
          field === "is_ingredient_cooking_method" ||
          field === "is_cost_manual"
        ) {
          // Normalize boolean values for comparison
          const normalizedOldValue = Boolean(oldValue);
          const normalizedNewValue = Boolean(newValue);
          valuesAreEqual = normalizedOldValue === normalizedNewValue;
        } else {
          valuesAreEqual = updateData[field] === (existingRecipe as any)[field];
        }

        if (!valuesAreEqual) {
          hasChanges = true;
        }
      }
    });

    if (hasChanges) {
      await Recipe.update(recipeUpdateData, {
        where: { id },
        transaction,
      });

      // Add basic info changes to consolidated changes (only actual changes)
      Object.keys(recipeUpdateData).forEach((key) => {
        if (key !== "updated_by" && recipeUpdateData[key] !== undefined) {
          const oldVal = (oldValues as any)[key];
          const newVal = recipeUpdateData[key];
          if (oldVal !== newVal) {
            const fieldDisplayName = key
              .replace(/_/g, " ")
              .replace(/\b\w/g, (l) => l.toUpperCase());
            allChanges.push(
              `${fieldDisplayName} changed from "${oldVal || "N/A"}" to "${newVal}".`
            );
            hasAnyChanges = true;
          }
        }
      });
    }

    // Update categories if provided AND different from existing
    if (updateData.categories !== undefined) {
      // Check if categories actually changed
      const existingCategoryIds = existingRecipe.categories
        ? existingRecipe.categories.map((cat: any) => cat.id).sort()
        : [];
      const newCategoryIds = updateData.categories.sort();
      const categoriesChanged =
        JSON.stringify(existingCategoryIds) !== JSON.stringify(newCategoryIds);

      await updateRecipeCategories(
        Number(id),
        updateData.categories,
        userId,
        organizationId,
        ipAddress,
        userAgent,
        transaction,
        false
      ); // Don't create individual history

      // Add detailed category changes with actual names ONLY if changed
      if (
        categoriesChanged &&
        updateData.categories &&
        updateData.categories.length > 0
      ) {
        try {
          // Fetch category names
          const categories = await sequelize.query(
            `SELECT category_name FROM mo_category WHERE id IN (${updateData.categories.join(",")}) AND organization_id = :organizationId`,
            {
              replacements: { organizationId },
              type: sequelize.QueryTypes.SELECT,
            }
          );
          if (categories && categories.length > 0) {
            const categoryNames = categories
              .map((cat: any) => cat.category_name)
              .join(", ");
            allChanges.push(`Categories: ${categoryNames}.`);
            hasAnyChanges = true;
          } else {
            // Fallback: try without organization_id filter
            const categoriesNoOrg = await sequelize.query(
              `SELECT category_name FROM mo_category WHERE id IN (${updateData.categories.join(",")})`,
              {
                type: sequelize.QueryTypes.SELECT,
              }
            );
            if (categoriesNoOrg && categoriesNoOrg.length > 0) {
              const categoryNames = categoriesNoOrg
                .map((cat: any) => cat.category_name)
                .join(", ");
              allChanges.push(`Categories: ${categoryNames}.`);
              hasAnyChanges = true;
            } else {
              allChanges.push(
                `Categories updated (${updateData.categories.length} categories).`
              );
              hasAnyChanges = true;
            }
          }
        } catch (_error) {
          allChanges.push(
            `Categories updated (${updateData.categories.length} categories).`
          );
          hasAnyChanges = true;
        }
      }
    }

    // Update attributes if provided (type-wise or legacy)
    const hasTypeWiseAttributes =
      updateData.nutrition_attributes !== undefined ||
      updateData.allergen_attributes !== undefined ||
      updateData.cuisine_attributes !== undefined ||
      updateData.dietary_attributes !== undefined ||
      updateData.haccp_attributes !== undefined;

    if (hasTypeWiseAttributes) {
      await updateRecipeAttributesTypewise(
        Number(id),
        {
          nutrition_attributes: updateData.nutrition_attributes,
          allergen_attributes: updateData.allergen_attributes,
          cuisine_attributes: updateData.cuisine_attributes,
          dietary_attributes: updateData.dietary_attributes,
          haccp_attributes: updateData.haccp_attributes,
        },
        userId,
        organizationId,
        ipAddress,
        userAgent,
        transaction,
        false
      ); // Don't create individual history

      // Add detailed nutrition attribute changes with proper unit names ONLY if actually changed
      if (
        updateData.nutrition_attributes &&
        updateData.nutrition_attributes.length > 0
      ) {
        // Check if nutrition attributes actually changed
        const existingNutritionAttrs = existingRecipe.attributes
          ? existingRecipe.attributes.filter(
            (attr: any) => attr.attribute_type === "nutrition"
          )
          : [];

        const nutritionChanged =
          JSON.stringify(
            existingNutritionAttrs
              .map((attr: any) => ({
                id: attr.id,
                unit: attr.RecipeAttributes?.unit,
                unit_of_measure: attr.RecipeAttributes?.unit_of_measure,
              }))
              .sort((a: any, b: any) => a.id - b.id)
          ) !==
          JSON.stringify(
            updateData.nutrition_attributes
              .map((attr: any) => ({
                id: attr.id,
                unit: attr.unit,
                unit_of_measure: attr.unit_of_measure,
              }))
              .sort((a: any, b: any) => a.id - b.id)
          );

        if (nutritionChanged) {
          for (const attr of updateData.nutrition_attributes) {
            if (attr.unit && attr.unit_of_measure) {
              try {
                // Fetch unit name
                const unitResult = await sequelize.query(
                  `SELECT unit_name FROM mo_unit WHERE id = :unitId AND organization_id = :organizationId`,
                  {
                    replacements: {
                      unitId: attr.unit_of_measure,
                      organizationId,
                    },
                    type: sequelize.QueryTypes.SELECT,
                  }
                );
                let unitName = attr.unit_of_measure;
                if (unitResult && unitResult.length > 0) {
                  unitName = (unitResult[0] as any).unit_name;
                } else {
                  // Fallback: try without organization_id filter
                  const unitResultNoOrg = await sequelize.query(
                    `SELECT unit_name FROM mo_unit WHERE id = :unitId`,
                    {
                      replacements: { unitId: attr.unit_of_measure },
                      type: sequelize.QueryTypes.SELECT,
                    }
                  );
                  if (unitResultNoOrg && unitResultNoOrg.length > 0) {
                    unitName = (unitResultNoOrg[0] as any).unit_name;
                  }
                }
                allChanges.push(
                  `Nutrient "${attr.description || "Unknown"}" set to "${attr.unit} ${unitName}".`
                );
                hasAnyChanges = true;
              } catch (_error) {
                allChanges.push(
                  `Nutrient "${attr.description || "Unknown"}" set to "${attr.unit} ${attr.unit_of_measure}".`
                );
                hasAnyChanges = true;
              }
            }
          }
        }
      }

      if (updateData.allergen_attributes) {
        // Check if allergen attributes actually changed
        const existingAllergenAttrs = existingRecipe.attributes
          ? existingRecipe.attributes.filter(
            (attr: any) => attr.attribute_type === "allergen"
          )
          : [];

        let allergenChanged = false;

        if (Array.isArray(updateData.allergen_attributes)) {
          const existingIds = existingAllergenAttrs
            .map((attr: any) => attr.id)
            .sort();
          const newIds = updateData.allergen_attributes.sort();
          allergenChanged =
            JSON.stringify(existingIds) !== JSON.stringify(newIds);
        } else if (
          updateData.allergen_attributes.contains ||
          updateData.allergen_attributes.may_contain
        ) {
          const existingContains = existingAllergenAttrs
            .filter((attr: any) => !attr.RecipeAttributes?.may_contain)
            .map((attr: any) => attr.id)
            .sort();
          const existingMayContain = existingAllergenAttrs
            .filter((attr: any) => attr.RecipeAttributes?.may_contain)
            .map((attr: any) => attr.id)
            .sort();
          const newContains = (
            updateData.allergen_attributes.contains || []
          ).sort();
          const newMayContain = (
            updateData.allergen_attributes.may_contain || []
          ).sort();

          allergenChanged =
            JSON.stringify(existingContains) !== JSON.stringify(newContains) ||
            JSON.stringify(existingMayContain) !==
            JSON.stringify(newMayContain);
        }

        if (allergenChanged) {
          if (Array.isArray(updateData.allergen_attributes)) {
            try {
              const allergens = await sequelize.query(
                `SELECT attribute_title FROM mo_attributes WHERE id IN (${updateData.allergen_attributes.join(",")}) AND organization_id = :organizationId`,
                {
                  replacements: { organizationId },
                  type: sequelize.QueryTypes.SELECT,
                }
              );
              if (allergens && allergens.length > 0) {
                const allergenNames = allergens
                  .map((allergen: any) => allergen.attribute_title)
                  .join(", ");
                allChanges.push(`Allergens: ${allergenNames}.`);
                hasAnyChanges = true;
              } else {
                allChanges.push(
                  `Allergens updated (${updateData.allergen_attributes.length} allergens).`
                );
                hasAnyChanges = true;
              }
            } catch (_error) {
              allChanges.push(
                `Allergens updated (${updateData.allergen_attributes.length} allergens).`
              );
              hasAnyChanges = true;
            }
          } else if (
            updateData.allergen_attributes.contains ||
            updateData.allergen_attributes.may_contain
          ) {
            if (
              updateData.allergen_attributes.contains &&
              updateData.allergen_attributes.contains.length > 0
            ) {
              try {
                const allergens = await sequelize.query(
                  `SELECT attribute_title FROM mo_attributes WHERE id IN (${updateData.allergen_attributes.contains.join(",")}) AND organization_id = :organizationId`,
                  {
                    replacements: { organizationId },
                    type: sequelize.QueryTypes.SELECT,
                  }
                );
                if (allergens && allergens.length > 0) {
                  const allergenNames = allergens
                    .map((allergen: any) => allergen.attribute_title)
                    .join(", ");
                  allChanges.push(`Allergens: ${allergenNames}.`);
                  hasAnyChanges = true;
                } else {
                  // Fallback: try without organization_id filter
                  const allergensNoOrg = await sequelize.query(
                    `SELECT attribute_title FROM mo_attributes WHERE id IN (${updateData.allergen_attributes.contains.join(",")})`,
                    {
                      type: sequelize.QueryTypes.SELECT,
                    }
                  );
                  if (allergensNoOrg && allergensNoOrg.length > 0) {
                    const allergenNames = allergensNoOrg
                      .map((allergen: any) => allergen.attribute_title)
                      .join(", ");
                    allChanges.push(`Allergens: ${allergenNames}.`);
                    hasAnyChanges = true;
                  } else {
                    allChanges.push(
                      `Allergens added (${updateData.allergen_attributes.contains.length} allergens).`
                    );
                    hasAnyChanges = true;
                  }
                }
              } catch (_error) {
                allChanges.push(
                  `Allergens added (${updateData.allergen_attributes.contains.length} allergens).`
                );
                hasAnyChanges = true;
              }
            }
            if (
              updateData.allergen_attributes.may_contain &&
              updateData.allergen_attributes.may_contain.length > 0
            ) {
              try {
                const mayContainAllergens = await sequelize.query(
                  `SELECT attribute_title FROM mo_attributes WHERE id IN (${updateData.allergen_attributes.may_contain.join(",")}) AND organization_id = :organizationId`,
                  {
                    replacements: { organizationId },
                    type: sequelize.QueryTypes.SELECT,
                  }
                );
                const mayContainNames = mayContainAllergens
                  .map((allergen: any) => allergen.attribute_title)
                  .join(", ");
                allChanges.push(`May contain allergens: ${mayContainNames}.`);
              } catch (_error) {
                allChanges.push(
                  `May contain allergens added (${updateData.allergen_attributes.may_contain.length} allergens).`
                );
              }
            }
          }
        }
      } else if (updateData.attributes !== undefined) {
        // Legacy support for single attributes array
        await updateRecipeAttributes(
          Number(id),
          updateData.attributes,
          userId,
          organizationId,
          ipAddress,
          userAgent,
          transaction,
          false
        ); // Don't create individual history
        if (updateData.attributes && updateData.attributes.length > 0) {
          allChanges.push(
            `Attributes updated (${updateData.attributes.length} attributes).`
          );
          hasAnyChanges = true;
        }
      }
    }

    // Update ingredients if provided (independent of attributes)
    if (updateData.ingredients !== undefined) {
      // Check if ingredients actually changed (compare full objects, not just IDs)
      const existingIngredients = existingRecipe.ingredients || [];
      const newIngredients = updateData.ingredients || [];

      // Normalize ingredients for comparison (remove technical fields)
      const normalizeIngredient = (ing: any) => ({
        id: ing.id,
        quantity: ing.quantity || ing.ingredient_quantity,
        measure: ing.measure || ing.ingredient_measure,
        wastage: ing.wastage || ing.ingredient_wastage,
        cost: ing.cost || ing.ingredient_cost,
        cooking_method: ing.cooking_method || ing.ingredient_cooking_method,
        preparation_method: ing.preparation_method,
      });

      const normalizedExisting = existingIngredients
        .map(normalizeIngredient)
        .sort((a: any, b: any) => a.id - b.id);
      const normalizedNew = newIngredients
        .map(normalizeIngredient)
        .sort((a: any, b: any) => a.id - b.id);

      const ingredientsChanged =
        JSON.stringify(normalizedExisting) !== JSON.stringify(normalizedNew);

      await updateRecipeIngredients(
        Number(id),
        updateData.ingredients,
        userId,
        organizationId,
        ipAddress,
        userAgent,
        transaction,
        existingRecipe.recipe_title,
        true // Create individual history
      );

      // Update ingredient costs timestamp
      await updateRecipeCostTimestamp(Number(id), transaction);

      // Add detailed ingredient changes with actual names ONLY if changed
      if (
        ingredientsChanged &&
        updateData.ingredients &&
        updateData.ingredients.length > 0
      ) {
        hasAnyChanges = true;

        for (const ingredient of updateData.ingredients) {
          try {
            // Fetch ingredient name
            const ingredientResult = await sequelize.query(
              `SELECT ingredient_name FROM mo_ingredient WHERE id = :ingredientId AND organization_id = :organizationId`,
              {
                replacements: { ingredientId: ingredient.id, organizationId },
                type: sequelize.QueryTypes.SELECT,
              }
            );
            const ingredientName =
              ingredientResult.length > 0
                ? (ingredientResult[0] as any).ingredient_name
                : `Ingredient ${ingredient.id}`;

            // Fetch unit name if measure is provided
            let unitName = ingredient.measure;
            if (ingredient.measure) {
              try {
                const unitResult = await sequelize.query(
                  `SELECT unit_name FROM mo_unit WHERE id = :unitId AND organization_id = :organizationId`,
                  {
                    replacements: {
                      unitId: ingredient.measure,
                      organizationId,
                    },
                    type: sequelize.QueryTypes.SELECT,
                  }
                );
                if (unitResult && unitResult.length > 0) {
                  unitName = (unitResult[0] as any).unit_name;
                } else {
                  // Fallback: try without organization_id filter
                  const unitResultNoOrg = await sequelize.query(
                    `SELECT unit_name FROM mo_unit WHERE id = :unitId`,
                    {
                      replacements: { unitId: ingredient.measure },
                      type: sequelize.QueryTypes.SELECT,
                    }
                  );
                  if (unitResultNoOrg && unitResultNoOrg.length > 0) {
                    unitName = (unitResultNoOrg[0] as any).unit_name;
                  }
                }
              } catch (_error) {
                // Keep original measure if unit fetch fails
              }
            }

            allChanges.push(`Ingredient "${ingredientName}" added.`);
            if (ingredient.quantity && ingredient.measure) {
              allChanges.push(
                `Weight before cooking "quantity: ${ingredient.quantity}, unit: ${unitName}" added.`
              );
              allChanges.push(
                `Weight after cooking "quantity: ${ingredient.quantity}, unit: ${unitName}" added.`
              );
            }
            if (ingredient.cost) {
              allChanges.push(`Cost per serving set to "${ingredient.cost}".`);
            }
          } catch (_error) {
            // Fallback to generic description
            if (ingredient.quantity && ingredient.measure) {
              allChanges.push(
                `Weight before cooking "quantity: ${ingredient.quantity}, unit: ${ingredient.measure}" added.`
              );
              allChanges.push(
                `Weight after cooking "quantity: ${ingredient.quantity}, unit: ${ingredient.measure}" added.`
              );
            }
            if (ingredient.cost) {
              allChanges.push(`Cost per serving set to "${ingredient.cost}".`);
            }
          }
        }
      }
    }

    // Update steps if provided
    if (updateData.steps !== undefined) {
      // Check if steps actually changed
      const existingStepsCount = existingRecipe.steps
        ? existingRecipe.steps.length
        : 0;
      const newStepsCount = updateData.steps.length;
      const stepsChanged = existingStepsCount !== newStepsCount;

      await updateRecipeSteps(
        Number(id),
        updateData.steps,
        userId,
        organizationId,
        ipAddress,
        userAgent,
        transaction,
        false,
        true // Enable smart merge
      ); // Don't create individual history
      // Check if steps actually changed
      if (updateData.steps && updateData.steps.length > 0) {
        allChanges.push(
          `Recipe steps updated (${updateData.steps.length} steps).`
        );
        hasAnyChanges = true;
      }
    }

    // Update resources if provided
    if (updateData.resources !== undefined) {
      // Check if resources actually changed
      const existingResourcesCount = existingRecipe.resources
        ? existingRecipe.resources.length
        : 0;
      const newResourcesCount = updateData.resources.length;
      const resourcesChanged = existingResourcesCount !== newResourcesCount;

      await updateRecipeResources(
        Number(id),
        updateData.resources,
        userId,
        organizationId,
        ipAddress,
        userAgent,
        transaction,
        false,
        true // Enable smart merge
      ); // Don't create individual history
      if (
        resourcesChanged &&
        updateData.resources &&
        updateData.resources.length > 0
      ) {
        allChanges.push(
          `Recipe resources updated (${updateData.resources.length} resources).`
        );
        hasAnyChanges = true;
      }
    }

    // After all updates, fetch new state for arrays
    const newValues = { ...updateData };
    const newIngredients = await RecipeIngredients.findAll({
      where: {
        recipe_id: id,
        recipe_ingredient_status: RecipeIngredientsStatus.active,
      },
      transaction,
      raw: true,
    });
    const newSteps = await RecipeSteps.findAll({
      where: { recipe_id: id, status: RecipeStepsStatus.active },
      transaction,
      raw: true,
    });
    const newResources = await RecipeResources.findAll({
      where: { recipe_id: id, status: RecipeResourceStatus.active },
      transaction,
      raw: true,
    });
    newValues.ingredients = newIngredients;
    newValues.steps = newSteps;
    newValues.resources = newResources;

    // Create ONE consolidated history entry ONLY if there are actual changes
    if (hasAnyChanges && allChanges.length > 0) {
      // Add the main update line at the beginning
      allChanges.unshift(
        `Recipe "${existingRecipe.recipe_title}" was updated.`
      );

      // Determine the primary field that was changed for better highlight tracking
      let primaryFieldName = "recipe_full_update";
      const changedFields = Object.keys(recipeUpdateData).filter(
        (key) => key !== "updated_by" && recipeUpdateData[key] !== undefined
      );

      if (changedFields.length === 1) {
        // If only one field was changed, use that as the primary field
        primaryFieldName = changedFields[0];
      } else if (changedFields.length > 1) {
        // If multiple fields were changed, prioritize the most important ones
        const priorityFields = [
          "recipe_title",
          "recipe_description",
          "recipe_preparation_time",
          "recipe_cook_time",
        ];
        const priorityField = changedFields.find((field) =>
          priorityFields.includes(field)
        );
        if (priorityField) {
          primaryFieldName = priorityField;
        }
      }

      await createRecipeHistory(
        {
          recipe_id: Number(id),
          action: RecipeHistoryAction.updated,
          field_name: "recipe_full_update",
          old_value: JSON.stringify(oldValues),
          new_value: JSON.stringify(newValues),
          description: allChanges.join("\n"),
          ip_address: ipAddress,
          user_agent: userAgent,
          organization_id: organizationId,
          created_by: userId,
        },
        transaction
      );
    }

    // Fetch updated recipe with all relations (inside transaction)
    const updatedRecipe = await Recipe.findByPk(id, {
      ...getRecipeQueryOptions(),
      transaction,
    });

    await transactionManager.commit();

    // Clear file operations after successful commit
    fileTracker.clear();

    // Add file URLs to the updated recipe data
    const recipeWithUrls = addFileUrlsToRecipe(updatedRecipe);

    // Determine which sections were updated for smart response
    const updatedSections: string[] = [];
    if (hasChanges) updatedSections.push("basic");
    if (updateData.categories !== undefined) updatedSections.push("categories");
    if (hasTypeWiseAttributes || updateData.attributes !== undefined)
      updatedSections.push("attributes");
    if (updateData.ingredients !== undefined)
      updatedSections.push("ingredients");
    if (updateData.steps !== undefined) updatedSections.push("steps");
    if (updateData.resources !== undefined) updatedSections.push("resources");

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("RECIPE_UPDATED_SUCCESSFULLY"),
    });
  } catch (error: unknown) {
    await transactionManager.rollback();
    await fileTracker.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error updating recipe"
    );
  }
};

/**
 * Helper function to update recipe categories
 */
const updateRecipeCategories = async (
  recipeId: number,
  categories: number[],
  userId: number,
  organizationId: string,
  ipAddress: string,
  userAgent: string,
  transaction: any,
  createHistory: boolean = true
) => {
  // Set all existing categories to inactive
  await RecipeCategory.update(
    { status: RecipeCategoryStatus.inactive, updated_by: userId },
    { where: { recipe_id: recipeId }, transaction }
  );

  // Create new active categories
  if (categories && categories.length > 0) {
    const categoryData = categories.map((categoryId: number) => ({
      recipe_id: recipeId,
      category_id: categoryId,
      status: RecipeCategoryStatus.active,
      organization_id: organizationId,
      created_by: userId,
      updated_by: userId,
    }));
    await RecipeCategory.bulkCreate(categoryData, {
      transaction,
      updateOnDuplicate: ["status", "updated_by"],
    });
  }

  // Create history only if requested
  if (createHistory) {
    await createRecipeHistory(
      {
        recipe_id: recipeId,
        action: RecipeHistoryAction.category_added,
        field_name: "categories",
        new_value: JSON.stringify(categories),
        description: `Recipe categories updated`,
        ip_address: ipAddress,
        user_agent: userAgent,
        organization_id: organizationId,
        created_by: userId,
      },
      transaction
    );
  }
};

/**
 * Helper function to update recipe attributes (type-wise)
 */
const updateRecipeAttributesTypewise = async (
  recipeId: number,
  attributesByType: {
    nutrition_attributes?: any[];
    allergen_attributes?:
    | { contains?: number[]; may_contain?: number[] }
    | number[];
    cuisine_attributes?: number[];
    dietary_attributes?: number[];
    haccp_attributes?: any[];
  },
  userId: number,
  organizationId: string,
  ipAddress: string,
  userAgent: string,
  transaction: any,
  createHistory: boolean = true
) => {
  // Set all existing attributes to inactive
  await RecipeAttributes.update(
    { status: RecipeAttributesStatus.inactive, updated_by: userId },
    { where: { recipe_id: recipeId }, transaction }
  );

  const allAttributeData: any[] = [];

  // Handle nutrition attributes (with units)
  if (
    attributesByType.nutrition_attributes &&
    attributesByType.nutrition_attributes.length > 0
  ) {
    const nutritionData = attributesByType.nutrition_attributes.map(
      (attr: any) => ({
        recipe_id: recipeId,
        attributes_id: attr.id,
        unit_of_measure: attr.unit_of_measure,
        unit: attr.unit,
        status: RecipeAttributesStatus.active,
        organization_id: organizationId,
        attribute_description: attr.description,
        created_by: userId,
        updated_by: userId,
      })
    );
    allAttributeData.push(...nutritionData);
  }

  // Handle allergen attributes (with may_contain support)
  if (attributesByType.allergen_attributes) {
    // Check if it's the new format with contains/may_contain
    if (
      typeof attributesByType.allergen_attributes === "object" &&
      !Array.isArray(attributesByType.allergen_attributes)
    ) {
      const { contains, may_contain } =
        attributesByType.allergen_attributes as any;

      // Validate: ensure no allergen is in both contains and may_contain
      const containsIds = contains || [];
      const mayContainIds = may_contain || [];
      const duplicates = containsIds.filter((id: number) =>
        mayContainIds.includes(id)
      );

      if (duplicates.length > 0) {
        throw new Error(
          `Allergen IDs ${duplicates.join(", ")} cannot be in both 'contains' and 'may_contain' lists`
        );
      }

      // Add main allergens (contains)
      if (containsIds.length > 0) {
        const containsData = containsIds.map((attrId: number) => ({
          recipe_id: recipeId,
          attributes_id: attrId,
          unit_of_measure: null,
          unit: null,
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          attribute_description: null,
          may_contain: false,
          created_by: userId,
          updated_by: userId,
        }));
        allAttributeData.push(...containsData);
      }

      // Add may_contain allergens
      if (mayContainIds.length > 0) {
        const mayContainData = mayContainIds.map((attrId: number) => ({
          recipe_id: recipeId,
          attributes_id: attrId,
          unit_of_measure: null,
          unit: null,
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          attribute_description: null,
          may_contain: true,
          created_by: userId,
          updated_by: userId,
        }));
        allAttributeData.push(...mayContainData);
      }
    } else if (Array.isArray(attributesByType.allergen_attributes)) {
      // Legacy format: simple array of IDs (treat as contains)
      const allergenData = attributesByType.allergen_attributes.map(
        (attrId: number) => ({
          recipe_id: recipeId,
          attributes_id: attrId,
          unit_of_measure: null,
          unit: null,
          status: RecipeAttributesStatus.active,
          organization_id: organizationId,
          attribute_description: null,
          may_contain: false,
          created_by: userId,
          updated_by: userId,
        })
      );
      allAttributeData.push(...allergenData);
    }
  }

  // Handle cuisine attributes (simple IDs)
  if (
    attributesByType.cuisine_attributes &&
    attributesByType.cuisine_attributes.length > 0
  ) {
    const cuisineData = attributesByType.cuisine_attributes.map(
      (attrId: number) => ({
        recipe_id: recipeId,
        attributes_id: attrId,
        unit_of_measure: null,
        unit: null,
        status: RecipeAttributesStatus.active,
        organization_id: organizationId,
        attribute_description: null,
        created_by: userId,
        updated_by: userId,
      })
    );
    allAttributeData.push(...cuisineData);
  }

  // Handle dietary attributes (simple IDs)
  if (
    attributesByType.dietary_attributes &&
    attributesByType.dietary_attributes.length > 0
  ) {
    const dietaryData = attributesByType.dietary_attributes.map(
      (attrId: number) => ({
        recipe_id: recipeId,
        attributes_id: attrId,
        unit_of_measure: null,
        unit: null,
        status: RecipeAttributesStatus.active,
        organization_id: organizationId,
        attribute_description: null,
        created_by: userId,
        updated_by: userId,
      })
    );
    allAttributeData.push(...dietaryData);
  }

  // Handle HACCP attributes (with description and use_default)
  if (
    attributesByType.haccp_attributes &&
    attributesByType.haccp_attributes.length > 0
  ) {
    const haccpData = attributesByType.haccp_attributes.map((attr: any) => ({
      recipe_id: recipeId,
      attributes_id: attr.id,
      unit_of_measure: null,
      unit: null,
      status: RecipeAttributesStatus.active,
      organization_id: organizationId,
      attribute_description: attr.description,
      use_default: attr.use_default || false,
      created_by: userId,
      updated_by: userId,
    }));
    allAttributeData.push(...haccpData);
  }

  // Bulk create all attributes if any exist
  if (allAttributeData.length > 0) {
    await RecipeAttributes.bulkCreate(allAttributeData, {
      transaction,
      updateOnDuplicate: [
        "unit_of_measure",
        "unit",
        "status",
        "attribute_description",
        "use_default",
        "updated_by",
      ],
    });
  }

  // Update nutrition timestamp if nutrition attributes were updated
  if (
    attributesByType.nutrition_attributes &&
    attributesByType.nutrition_attributes.length > 0
  ) {
    await updateRecipeNutritionTimestamp(recipeId, transaction);
  }

  // Create detailed history record only if requested
  if (createHistory) {
    const detailedDescription = generateDetailedChangeDescription(
      "updated",
      "nutrition_attributes",
      null,
      attributesByType.nutrition_attributes || [],
      "Recipe"
    );

    await createRecipeHistory(
      {
        recipe_id: recipeId,
        action: RecipeHistoryAction.updated,
        field_name: "recipe_attributes_typewise",
        new_value: JSON.stringify(attributesByType),
        description: detailedDescription,
        ip_address: ipAddress,
        user_agent: userAgent,
        organization_id: organizationId,
        created_by: userId,
      },
      transaction
    );
  }
};

/**
 * Helper function to update recipe attributes (legacy)
 */
const updateRecipeAttributes = async (
  recipeId: number,
  attributes: any[],
  userId: number,
  organizationId: string,
  ipAddress: string,
  userAgent: string,
  transaction: any,
  createHistory: boolean = true
) => {
  // Set all existing attributes to inactive
  await RecipeAttributes.update(
    { status: RecipeAttributesStatus.inactive, updated_by: userId },
    { where: { recipe_id: recipeId }, transaction }
  );

  // Create new active attributes
  if (attributes && attributes.length > 0) {
    const attributeData = attributes.map((attr: any) => ({
      recipe_id: recipeId,
      attributes_id: attr.id,
      unit_of_measure: attr.unit_of_measure,
      unit: attr.unit,
      status: RecipeAttributesStatus.active,
      organization_id: organizationId,
      attribute_description: attr.description,
      created_by: userId,
      updated_by: userId,
    }));
    await RecipeAttributes.bulkCreate(attributeData, {
      transaction,
      updateOnDuplicate: [
        "unit_of_measure",
        "unit",
        "status",
        "attribute_description",
        "updated_by",
      ],
    });
  }

  // Create history only if requested
  if (createHistory) {
    await createRecipeHistory(
      {
        recipe_id: recipeId,
        action: RecipeHistoryAction.attribute_added,
        field_name: "attributes",
        new_value: JSON.stringify(attributes),
        description: `Recipe attributes updated`,
        ip_address: ipAddress,
        user_agent: userAgent,
        organization_id: organizationId,
        created_by: userId,
      },
      transaction
    );
  }
};

/**
 * Helper function to update recipe ingredients
 */
const updateRecipeIngredients = async (
  recipeId: number,
  ingredients: any[],
  userId: number,
  organizationId: string,
  ipAddress: string,
  userAgent: string,
  transaction: any,
  recipeName?: string,
  createHistory: boolean = true
) => {
  // Fetch current (old) ingredients before updating
  const oldIngredients = await RecipeIngredients.findAll({
    where: {
      recipe_id: recipeId,
      recipe_ingredient_status: RecipeIngredientsStatus.active,
    },
    transaction,
    raw: true,
  });

  // Set all existing ingredients to inactive
  await RecipeIngredients.update(
    {
      recipe_ingredient_status: RecipeIngredientsStatus.inactive,
      updated_by: userId,
    },
    { where: { recipe_id: recipeId }, transaction }
  );

  // Create new active ingredients
  if (ingredients && ingredients.length > 0) {
    const ingredientData = ingredients.map((ing: any) => ({
      recipe_id: recipeId,
      ingredient_id: ing.id,
      recipe_ingredient_status: RecipeIngredientsStatus.active,
      ingredient_quantity: ing.quantity,
      ingredient_measure: ing.measure,
      ingredient_wastage: ing.wastage,
      ingredient_cost: ing.cost,
      ingredient_cooking_method: ing.cooking_method,
      preparation_method: ing.preparation_method,
      organization_id: organizationId,
      created_by: userId,
      updated_by: userId,
    }));
    await RecipeIngredients.bulkCreate(ingredientData, {
      transaction,
      updateOnDuplicate: [
        "recipe_ingredient_status",
        "ingredient_quantity",
        "ingredient_measure",
        "ingredient_wastage",
        "ingredient_cost",
        "ingredient_cooking_method",
        "preparation_method",
        "updated_by",
      ],
    });
  }

  // Create detailed history only if requested
  if (createHistory) {
    const detailedDescription = generateDetailedChangeDescription(
      "updated",
      "ingredients",
      oldIngredients,
      ingredients,
      recipeName || "Recipe"
    );

    await createRecipeHistory(
      {
        recipe_id: recipeId,
        action: RecipeHistoryAction.ingredient_updated,
        field_name: "ingredients",
        old_value: JSON.stringify(oldIngredients),
        new_value: JSON.stringify(ingredients),
        description: detailedDescription,
        ip_address: ipAddress,
        user_agent: userAgent,
        organization_id: organizationId,
        created_by: userId,
      },
      transaction
    );
  }
};

/**
 * Helper function to update recipe steps
 */
const updateRecipeSteps = async (
  recipeId: number,
  steps: any[],
  userId: number,
  organizationId: string,
  ipAddress: string,
  userAgent: string,
  transaction: any,
  createHistory: boolean = true,
  smartMerge: boolean = true
) => {
  // Fetch current (old) steps before updating
  const oldSteps = await RecipeSteps.findAll({
    where: { recipe_id: recipeId, status: RecipeStepsStatus.active },
    transaction,
    raw: true,
  });

  if (smartMerge && (!steps || steps.length === 0)) {
    // No steps provided - keep existing steps
    return;
  }

  // Always delete existing steps (current behavior maintained)
  // This is because steps have order dependencies
  await RecipeSteps.destroy({
    where: { recipe_id: recipeId },
    transaction,
  });

  // Create new steps with item_id validation
  if (steps && steps.length > 0) {
    // Validate item_ids before creating steps
    const validatedSteps = [];
    for (const step of steps) {
      const validatedStep = {
        recipe_id: recipeId,
        item_id: null, // Default to null
        recipe_step_order: step.order,
        recipe_step_description: step.description,
        status: RecipeStepsStatus.active,
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      };

      // Validate item_id if provided
      if (step.item_id) {
        try {
          const itemExists = await Item.findByPk(step.item_id, {
            transaction,
          });
          if (itemExists) {
            validatedStep.item_id = step.item_id;
          } else {
            // Invalid item_id, setting to null
          }
        } catch (error: any) {
          // Error validating item_id, setting to null
        }
      }

      validatedSteps.push(validatedStep);
    }

    await RecipeSteps.bulkCreate(validatedSteps, { transaction });
  }

  // Create history only if requested
  if (createHistory) {
    await createRecipeHistory(
      {
        recipe_id: recipeId,
        action: RecipeHistoryAction.step_updated,
        field_name: "steps",
        old_value: JSON.stringify(oldSteps),
        new_value: JSON.stringify(steps),
        description: `Recipe steps updated`,
        ip_address: ipAddress,
        user_agent: userAgent,
        organization_id: organizationId,
        created_by: userId,
      },
      transaction
    );
  }
};

/**
 * Helper function to update recipe resources
 */
const updateRecipeResources = async (
  recipeId: number,
  resources: any[],
  userId: number,
  organizationId: string,
  ipAddress: string,
  userAgent: string,
  transaction: any,
  createHistory: boolean = true,
  smartMerge: boolean = true
) => {
  // Fetch current (old) resources before updating
  const oldResources = await RecipeResources.findAll({
    where: { recipe_id: recipeId, status: RecipeResourceStatus.active },
    transaction,
    raw: true,
  });

  // Get current active resources for deduplication
  const currentResources = await RecipeResources.findAll({
    where: {
      recipe_id: recipeId,
      status: RecipeResourceStatus.active,
    },
    transaction,
  });

  // Create separate maps for existing resources by item_id and item_link
  const existingItemResourceMap = new Map();
  const existingLinkResourceMap = new Map();

  currentResources.forEach((resource: any) => {
    if (resource.type === "item" && resource.item_id) {
      existingItemResourceMap.set(resource.item_id, resource);
    } else if (resource.type === "link" && resource.item_link) {
      existingLinkResourceMap.set(resource.item_link, resource);
    }
  });

  if (smartMerge && (!resources || resources.length === 0)) {
    // No new resources provided - keep existing ones
    return;
  }

  // Prepare arrays for batch operations
  const resourcesToCreate: any[] = [];
  const resourcesToUpdate: any[] = [];
  const processedItemIds = new Set();
  const processedLinks = new Set();

  // Process new/updated resources with proper deduplication
  for (const resource of resources) {
    // Validate resource based on type
    if (resource.type === "link") {
      if (!resource.item_link) {
        continue;
      }

      // Skip if we've already processed this link
      if (processedLinks.has(resource.item_link)) {
        continue;
      }

      const existingResource = existingLinkResourceMap.get(resource.item_link);

      if (existingResource) {
        // Update existing link resource
        resourcesToUpdate.push({
          id: existingResource.id,
          type: "link",
          item_link: resource.item_link,
          item_link_type:
            resource.item_link_type || existingResource.item_link_type,
          status: RecipeResourceStatus.active,
          updated_by: userId,
        });
      } else {
        // Create new link resource
        resourcesToCreate.push({
          recipe_id: recipeId,
          type: "link",
          item_id: null,
          item_link: resource.item_link,
          item_link_type: resource.item_link_type || "link",
          status: RecipeResourceStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        });
      }

      processedLinks.add(resource.item_link);
    } else {
      // Handle item type resources
      if (!resource.item_id) {
        continue;
      }

      // Skip if we've already processed this item_id
      if (processedItemIds.has(resource.item_id)) {
        continue;
      }

      const existingResource = existingItemResourceMap.get(resource.item_id);

      if (existingResource) {
        // Update existing item resource
        resourcesToUpdate.push({
          id: existingResource.id,
          type: "item",
          item_id: resource.item_id,
          item_link: null,
          item_link_type: null,
          status: RecipeResourceStatus.active,
          updated_by: userId,
        });
      } else {
        // Create new item resource
        resourcesToCreate.push({
          recipe_id: recipeId,
          type: "item",
          item_id: resource.item_id,
          item_link: null,
          item_link_type: null,
          status: RecipeResourceStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        });
      }

      processedItemIds.add(resource.item_id);
    }
  }

  if (resourcesToCreate.length > 0) {
    await RecipeResources.bulkCreate(resourcesToCreate, { transaction });
  }

  if (resourcesToUpdate.length > 0) {
    for (const resource of resourcesToUpdate) {
      await RecipeResources.update(
        {
          type: resource.type,
          item_link: resource.item_link,
          item_link_type: resource.item_link_type,
          status: resource.status,
          updated_by: resource.updated_by,
        },
        {
          where: { id: resource.id },
          transaction,
        }
      );
    }
  }

  // Create history only if requested
  if (createHistory) {
    await createRecipeHistory(
      {
        recipe_id: recipeId,
        action: RecipeHistoryAction.resource_added,
        field_name: "resources",
        old_value: JSON.stringify(oldResources),
        new_value: JSON.stringify(resources),
        description: `Recipe resources updated`,
        ip_address: ipAddress,
        user_agent: userAgent,
        organization_id: organizationId,
        created_by: userId,
      },
      transaction
    );
  }
};

/**
 * @description Get recipe history by recipe ID or slug
 * @route GET /api/v1/recipes/history/:id
 * @access Private
 */
const getRecipeHistory = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { page, limit, size, action, user_id, filter } = req.query;
    const organizationId = (req as any).user?.organization_id;

    // Dynamic pagination - only apply if size/limit is provided
    const pageSize = size || limit;

    // Determine if identifier is numeric (ID) or string (slug)
    const isNumeric = !isNaN(Number(id));

    // Define action categories for filtering
    const activityActions = [
      RecipeHistoryAction.bookmark_added,
      RecipeHistoryAction.bookmark_removed,
      RecipeHistoryAction.created,
      RecipeHistoryAction.published,
      RecipeHistoryAction.archived,
      RecipeHistoryAction.restored,
      RecipeHistoryAction.deleted,
    ];

    const historyActions = [
      RecipeHistoryAction.updated,
      RecipeHistoryAction.ingredient_added,
      RecipeHistoryAction.ingredient_removed,
      RecipeHistoryAction.ingredient_updated,
      RecipeHistoryAction.step_added,
      RecipeHistoryAction.step_removed,
      RecipeHistoryAction.step_updated,
      RecipeHistoryAction.category_added,
      RecipeHistoryAction.category_removed,
      RecipeHistoryAction.attribute_added,
      RecipeHistoryAction.attribute_removed,
      RecipeHistoryAction.resource_added,
      RecipeHistoryAction.resource_removed,
    ];

    // Build where conditions
    const whereConditions: any = {};
    if (organizationId) {
      whereConditions.organization_id = organizationId;
    }
    if (action) {
      whereConditions.action = action;
    }
    if (user_id) {
      whereConditions.created_by = user_id;
    }

    // Apply filter parameter
    if (filter) {
      if (filter === "activity") {
        whereConditions.action = { [Op.in]: activityActions };
      } else if (filter === "history") {
        whereConditions.action = { [Op.in]: historyActions };
      }
      // If filter is provided but not 'activity' or 'history', ignore it (maintain backward compatibility)
    }

    if (isNumeric) {
      whereConditions.recipe_id = Number(id);
    } else {
      // If slug is provided, we need to ensure we fetch the correct recipe
      whereConditions.recipe_id = {
        [Op.in]: sequelize.literal(
          `(SELECT id FROM mo_recipe WHERE recipe_slug = '${id}' AND organization_id = '${organizationId}')`
        ),
      };
    }

    const queryOptions: any = {
      where: whereConditions,
      include: [
        {
          model: Recipe,
          as: "recipe",
          attributes: ["id", "recipe_title", "recipe_slug"],
          required: true,
          ...(isNumeric ? {} : { where: { recipe_slug: id } }),
        },
        {
          model: User,
          as: "creator",
          attributes: ["id", "user_email"],
          required: false,
        },
      ],
      order: [["created_at", "DESC"]],
    };

    // Add pagination only if pageSize is provided
    if (pageSize) {
      const { offset } = getPagination(Number(page), Number(pageSize));
      queryOptions.limit = Number(pageSize);
      queryOptions.offset = offset;
    }

    const { count, rows: history } =
      await RecipeHistory.findAndCountAll(queryOptions);

    let formattedHistory: any[] = [];
    if (history && history.length > 0) {
      // Format history data to match your reference format
      formattedHistory = await Promise.all(
        history.map(async (record: any) => {
          const createdAt = new Date(record.created_at);
          const formattedDate = createdAt.toLocaleDateString("en-GB", {
            day: "2-digit",
            month: "2-digit",
            year: "numeric",
          });
          const formattedTime = createdAt.toLocaleTimeString("en-GB", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: false,
          });

          // Get user details from nv_users table
          let userName = "Unknown User";
          if (record.created_by) {
            try {
              const userDetails = await getUser(record.created_by);
              if (userDetails) {
                userName =
                  userDetails.user_full_name ||
                  `${userDetails.user_first_name || ""} ${userDetails.user_last_name || ""}`.trim() ||
                  userDetails.user_email ||
                  "Unknown User";
              } else {
                // Fallback to email from User model if available
                userName = record.creator?.user_email || "Unknown User";
              }
            } catch (_error) {
              // Fallback to email if available
              userName = record.creator?.user_email || "Unknown User";
            }
          }

          return {
            id: record.id,
            date: `${formattedDate}, ${formattedTime}`,
            user: userName,
            changes: record.description || `Recipe ${record.action}`,
            action: record.action,
            field_name: record.field_name,
            old_value: record.old_value,
            new_value: record.new_value,
            created_at: record.created_at,
            recipe_title: record.recipe?.recipe_title,
          };
        })
      );
    }
    // Calculate pagination info only if pageSize is provided
    let total_pages = 0;
    if (pageSize) {
      const paginationInfo = getPaginatedItems(
        Number(pageSize),
        Number(page),
        count || 0
      );
      total_pages = paginationInfo.total_pages;
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("RECIPE_HISTORY_FETCHED_SUCCESSFULLY"),
      count: count,
      data: formattedHistory || [],
      page: Number(page) || 1,
      size: pageSize ? Number(pageSize) : count,
      total_pages: total_pages,
    });
  } catch (error: unknown) {
    const customError = error as CustomError;
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_FETCHING_RECIPE_HISTORY"),
      error: customError.message,
    });
  }
};

/**
 * @description Duplicate latest recipe
 * @route POST /api/v1/recipes/duplicate/:id
 * @access Private
 */
const duplicateRecipe = async (req: Request, res: Response): Promise<any> => {
  const transaction = await sequelize.transaction();
  try {
    const { id } = req.params;
    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;
    const ipAddress = req.ip;
    const userAgent = req.get("User-Agent");

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS"),
      });
    }

    // Find original recipe with all relations
    const originalRecipe = await Recipe.findOne({
      where: {
        id,
        organization_id: organizationId,
      },
      ...getRecipeQueryOptions(),
    });

    if (!originalRecipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("RECIPE_NOT_FOUND"),
      });
    }

    // Generate unique slug for duplicate
    const duplicateTitle = `${originalRecipe.recipe_title} (Copy)`;

    // Create function to check if slug exists within the transaction
    const checkSlugExists = async (slug: string): Promise<boolean> => {
      const existingRecipe = await Recipe.findOne({
        where: {
          recipe_slug: slug,
          organization_id: organizationId,
          recipe_status: {
            [Op.not]: RecipeStatus.deleted,
          },
        },
        transaction, // Use transaction to prevent race conditions
        lock: true, // Add row-level lock for consistency
      });
      return !!existingRecipe;
    };

    // Use a simpler, more reliable approach for generating unique slug
    let recipe_slug = ""; // Initialize the variable
    let attempts = 0;
    const maxDbAttempts = 10;

    // Start with a UUID-based approach to ensure uniqueness
    const baseSlug = generateSlug(duplicateTitle, {
      maxLength: 20, // Leave room for suffix
      separator: "-",
      lowercase: true,
    });

    while (attempts < maxDbAttempts) {
      if (attempts === 0) {
        // First attempt: use the base slug
        recipe_slug = baseSlug;
      } else if (attempts < 5) {
        // Next few attempts: use counter
        recipe_slug = `${baseSlug}-${attempts}`;
      } else {
        // Final attempts: use timestamp + random
        const uuid = randomUUID().substring(0, 16);
        recipe_slug = `${baseSlug}-${uuid}`;
      }

      // Check if this slug exists
      const slugExists = await checkSlugExists(recipe_slug);
      if (!slugExists) {
        break;
      }

      attempts++;
    }

    // // If all attempts failed, use guaranteed unique slug
    // if (attempts >= maxDbAttempts) {
    //   const uuid = randomUUID().substring(0, 8);
    //   recipe_slug = `${baseSlug}-${uuid}`;
    // }

    // Create duplicate recipe with all fields from create recipe API
    const currentTimestamp = new Date();
    const newDuplicateRecipe = await Recipe.create(
      {
        recipe_title: duplicateTitle,
        recipe_public_title: originalRecipe.recipe_public_title
          ? `${originalRecipe.recipe_public_title} (Copy)`
          : null,
        recipe_description: originalRecipe.recipe_description,
        recipe_preparation_time: originalRecipe.recipe_preparation_time,
        recipe_cook_time: originalRecipe.recipe_cook_time,
        has_recipe_public_visibility: false, // Set to private by default
        has_recipe_private_visibility:
          originalRecipe.has_recipe_private_visibility,
        recipe_status: RecipeStatus.draft, // Set to draft by default
        recipe_serve_in: originalRecipe.recipe_serve_in,
        recipe_complexity_level: originalRecipe.recipe_complexity_level,
        recipe_garnish: originalRecipe.recipe_garnish,
        recipe_head_chef_tips: originalRecipe.recipe_head_chef_tips,
        recipe_foh_tips: originalRecipe.recipe_foh_tips,
        recipe_impression: 0, // Reset impression count
        recipe_yield: originalRecipe.recipe_yield,
        recipe_yield_unit: originalRecipe.recipe_yield_unit,
        recipe_total_portions: originalRecipe.recipe_total_portions,
        recipe_single_portion_size: originalRecipe.recipe_single_portion_size,
        recipe_serving_method: originalRecipe.recipe_serving_method,
        recipe_placeholder: originalRecipe.recipe_placeholder,
        recipe_slug,
        ingredient_costs_updated_at: currentTimestamp,
        nutrition_values_updated_at: currentTimestamp,
        vitamin_a: originalRecipe.vitamin_a,
        vitamin_c: originalRecipe.vitamin_c,
        calcium: originalRecipe.calcium,
        iron: originalRecipe.iron,
        is_ingredient_cooking_method:
          originalRecipe.is_ingredient_cooking_method,
        is_preparation_method: originalRecipe.is_preparation_method,
        is_cost_manual: originalRecipe.is_cost_manual,
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      },
      { transaction }
    );

    // Duplicate categories
    if (originalRecipe.categories && originalRecipe.categories.length > 0) {
      const categoryData = originalRecipe.categories.map((category: any) => ({
        recipe_id: newDuplicateRecipe.id,
        category_id: category.id,
        status: RecipeCategoryStatus.active,
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      }));
      await RecipeCategory.bulkCreate(categoryData, { transaction });
    }

    // Duplicate attributes
    if (originalRecipe.attributes && originalRecipe.attributes.length > 0) {
      const attributeData = originalRecipe.attributes.map((attr: any) => ({
        recipe_id: newDuplicateRecipe.id,
        attributes_id: attr.id,
        unit_of_measure: attr.RecipeAttributes?.unit_of_measure,
        unit: attr.RecipeAttributes?.unit,
        status: RecipeAttributesStatus.active,
        organization_id: organizationId,
        attribute_description: attr.RecipeAttributes?.attribute_description,
        may_contain: attr.RecipeAttributes?.may_contain,
        created_by: userId,
        updated_by: userId,
      }));
      await RecipeAttributes.bulkCreate(attributeData, { transaction });
    }

    // Duplicate ingredients
    if (originalRecipe.ingredients && originalRecipe.ingredients.length > 0) {
      const ingredientData = originalRecipe.ingredients.map((ing: any) => ({
        recipe_id: newDuplicateRecipe.id,
        ingredient_id: ing.id,
        recipe_ingredient_status: RecipeIngredientsStatus.active,
        ingredient_quantity: ing.RecipeIngredients?.ingredient_quantity,
        ingredient_measure: ing.RecipeIngredients?.ingredient_measure,
        ingredient_wastage: ing.RecipeIngredients?.ingredient_wastage,
        ingredient_cost: ing.RecipeIngredients?.ingredient_cost,
        ingredient_cooking_method:
          ing.RecipeIngredients?.ingredient_cooking_method,
        preparation_method: ing.RecipeIngredients?.preparation_method,
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      }));
      await RecipeIngredients.bulkCreate(ingredientData, { transaction });
    }

    // Duplicate steps
    if (originalRecipe.steps && originalRecipe.steps.length > 0) {
      const stepData = originalRecipe.steps.map((step: any) => ({
        recipe_id: newDuplicateRecipe.id,
        item_id: step.item_id,
        recipe_step_order: step.recipe_step_order,
        recipe_step_description: step.recipe_step_description,
        status: RecipeStepsStatus.active,
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      }));
      await RecipeSteps.bulkCreate(stepData, { transaction });
    }

    // Duplicate resources
    if (originalRecipe.resources && originalRecipe.resources.length > 0) {
      const resourceData = originalRecipe.resources.map((resource: any) => ({
        recipe_id: newDuplicateRecipe.id,
        type: resource.type,
        item_id: resource.item_id,
        item_link: resource.item_link,
        item_link_type: resource.item_link_type,
        status: RecipeResourceStatus.active,
        organization_id: organizationId,
        created_by: userId,
        updated_by: userId,
      }));
      await RecipeResources.bulkCreate(resourceData, { transaction });
    }

    // Create history record
    await createRecipeHistory(
      {
        recipe_id: newDuplicateRecipe.id,
        action: RecipeHistoryAction.created,
        field_name: "recipe_duplicated",
        description: `Recipe duplicated from "${originalRecipe.recipe_title}" (ID: ${originalRecipe.id})`,
        ip_address: ipAddress,
        user_agent: userAgent,
        organization_id: organizationId,
        created_by: userId,
      },
      transaction
    );

    await transaction.commit();

    // Get the duplicated recipe in the same format as recipe list API
    const recipeInListFormat = await getRecipeInListFormat(
      newDuplicateRecipe.id,
      userId,
      organizationId
    );

    return res.status(StatusCodes.CREATED).json({
      status: true,
      message: res.__("RECIPE_DUPLICATED_SUCCESSFULLY"),
      data: recipeInListFormat,
    });
  } catch (error: unknown) {
    // Only rollback if transaction is still active
    if (!transaction.finished) {
      await transaction.rollback();
    }

    const customError = error as CustomError;

    // Handle specific duplicate entry errors
    if (
      customError.message &&
      customError.message.includes("Duplicate entry") &&
      customError.message.includes("unique_recipe_slug_per_org")
    ) {
      return res.status(StatusCodes.CONFLICT).json({
        status: false,
        message: res.__("RECIPE_SLUG_ALREADY_EXISTS"),
        error: "A recipe with this slug already exists. Please try again.",
        error_code: "DUPLICATE_SLUG_ERROR",
      });
    }

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_DUPLICATING_RECIPE"),
      error: customError.message || "Unknown error occurred",
    });
  }
};

/**
 * @description Add/Remove recipe bookmark
 * @route POST /api/v1/recipes/bookmark/:id
 * @access Private
 */
const toggleRecipeBookmark = async (
  req: Request,
  res: Response
): Promise<any> => {
  const transaction = await sequelize.transaction();
  try {
    const { id } = req.params;
    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;
    const ipAddress = req.ip;
    const userAgent = req.get("User-Agent") || "Unknown";

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS"),
      });
    }

    // Check if recipe exists
    const recipe = await Recipe.findOne({
      where: {
        id,
        organization_id: organizationId,
      },
    });

    if (!recipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("RECIPE_NOT_FOUND"),
      });
    }

    // Check if bookmark already exists
    const existingBookmark = await RecipeBookmarks.findOne({
      where: {
        recipe_id: id,
        user_id: userId,
      },
    });

    let isBookmarked = false;
    let action = "";

    if (existingBookmark) {
      if (existingBookmark.status === RecipeBookmarkStatus.active) {
        // Remove bookmark
        await RecipeBookmarks.update(
          {
            status: RecipeBookmarkStatus.inactive,
            updated_by: userId,
          },
          {
            where: { recipe_id: id, user_id: userId },
            transaction,
          }
        );
        action = "removed";

        // Create history
        await createRecipeHistory(
          {
            recipe_id: Number(id),
            action: RecipeHistoryAction.bookmark_removed,
            field_name: "recipe_bookmark",
            old_value: "true",
            new_value: "false",
            description: `Recipe bookmark removed`,
            ip_address: ipAddress,
            user_agent: userAgent,
            organization_id: organizationId,
            created_by: userId,
          },
          transaction
        );
      } else {
        // Reactivate bookmark
        await RecipeBookmarks.update(
          {
            status: RecipeBookmarkStatus.active,
            updated_by: userId,
          },
          {
            where: { recipe_id: id, user_id: userId },
            transaction,
          }
        );
        isBookmarked = true;
        action = "added";

        // Create history
        await createRecipeHistory(
          {
            recipe_id: Number(id),
            action: RecipeHistoryAction.bookmark_added,
            field_name: "recipe_bookmark",
            old_value: "false",
            new_value: "true",
            description: `Recipe bookmark added`,
            ip_address: ipAddress,
            user_agent: userAgent,
            organization_id: organizationId,
            created_by: userId,
          },
          transaction
        );
      }
    } else {
      // Create new bookmark
      await RecipeBookmarks.create(
        {
          recipe_id: Number(id),
          user_id: userId,
          status: RecipeBookmarkStatus.active,
          organization_id: organizationId,
          created_by: userId,
          updated_by: userId,
        },
        { transaction }
      );

      isBookmarked = true;
      action = "added";

      // Create history
      await createRecipeHistory(
        {
          recipe_id: Number(id),
          action: RecipeHistoryAction.bookmark_added,
          field_name: "recipe_bookmark",
          old_value: "false",
          new_value: "true",
          description: `Recipe bookmark added`,
          ip_address: ipAddress,
          user_agent: userAgent,
          organization_id: organizationId,
          created_by: userId,
        },
        transaction
      );
    }

    await transaction.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__(
        isBookmarked ? "RECIPE_BOOKMARKED" : "RECIPE_BOOKMARK_REMOVED"
      ),
    });
  } catch (error: unknown) {
    // Only rollback if transaction is still active
    if (!transaction.finished) {
      await transaction.rollback();
    }
    const customError = error as CustomError;
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_UPDATING_BOOKMARK"),
      error: customError.message,
    });
  }
};

/**
 * @description Export single recipe
 * @route GET /api/v1/recipes/export/:id
 * @access Private
 */
const exportRecipe = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const { format = "excel" } = req.query;
    const organizationId = (req as any).user?.organization_id;

    // Use the optimized raw query function to get complete recipe data with URLs
    const recipe = await getRecipeByIdRaw(Number(id), organizationId);

    if (!recipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("RECIPE_NOT_FOUND"),
      });
    }

    return await handleRecipeExport({}, [recipe], format as string, res, req);
  } catch (error: unknown) {
    const customError = error as CustomError;
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_EXPORTING_RECIPE"),
      error: customError.message,
    });
  }
};

/**
 * @description Publish Recipe (Change from draft to published)
 * @route POST /api/v1/recipes/:id/publish
 * @access Private
 */
const publishRecipe = async (req: Request, res: Response): Promise<any> => {
  const transaction = await sequelize.transaction();
  try {
    const { id } = req.params;
    const { has_recipe_public_visibility, has_recipe_private_visibility } =
      req.body;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;
    const ipAddress = req.ip || "Unknown";
    const userAgent = req.get("User-Agent") || "Unknown";

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS"),
      });
    }

    // Find existing recipe
    const existingRecipe = await Recipe.findOne({
      where: {
        id,
        organization_id: organizationId,
      },
    });

    if (!existingRecipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("RECIPE_NOT_FOUND"),
      });
    }

    // Check if recipe is in draft status
    if (existingRecipe.recipe_status !== RecipeStatus.draft) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("RECIPE_ALREADY_PUBLISHED"),
        currentStatus: existingRecipe.recipe_status,
      });
    }

    // Update recipe to published status
    const updateData: any = {
      recipe_status: RecipeStatus.publish,
      updated_by: userId,
    };

    // Update visibility if provided
    if (has_recipe_public_visibility !== undefined) {
      updateData.has_recipe_public_visibility = has_recipe_public_visibility;
    }
    if (has_recipe_private_visibility !== undefined) {
      updateData.has_recipe_private_visibility = has_recipe_private_visibility;
    }

    await Recipe.update(updateData, {
      where: { id },
      transaction,
    });

    // Create history record
    await createRecipeHistory(
      {
        recipe_id: Number(id),
        action: RecipeHistoryAction.updated,
        field_name: "recipe_status",
        old_value: RecipeStatus.draft,
        new_value: RecipeStatus.publish,
        description: `Recipe published with visibility settings`,
        ip_address: ipAddress,
        user_agent: userAgent,
        organization_id: organizationId,
        created_by: userId,
      },
      transaction
    );

    await transaction.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("RECIPE_PUBLISHED_SUCCESSFULLY"),
    });
  } catch (error: unknown) {
    if (!transaction.finished) {
      await transaction.rollback();
    }
    const customError = error as CustomError;

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_PUBLISHING_RECIPE"),
      error: customError.message || "Unknown error occurred",
    });
  }
};

/**
 * @description Make Recipe Public/Private
 * @route PATCH /api/v1/recipes/:id/make-public
 * @access Private
 */
const makeRecipePublic = async (req: Request, res: Response): Promise<any> => {
  const transaction = await sequelize.transaction();
  try {
    const { id } = req.params;
    const { has_recipe_public_visibility, has_recipe_private_visibility } =
      req.body;

    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;
    const ipAddress = req.ip || "Unknown";
    const userAgent = req.get("User-Agent") || "Unknown";

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: res.__("UNAUTHORIZED_ACCESS"),
      });
    }

    // Find existing recipe
    const existingRecipe = await Recipe.findOne({
      where: {
        id,
        organization_id: organizationId,
      },
    });

    if (!existingRecipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("RECIPE_NOT_FOUND"),
      });
    }

    // Update visibility settings
    const updateData: any = {
      has_recipe_public_visibility,
      updated_by: userId,
    };

    if (has_recipe_private_visibility !== undefined) {
      updateData.has_recipe_private_visibility = has_recipe_private_visibility;
    }

    await Recipe.update(updateData, {
      where: { id },
      transaction,
    });

    // Create history record
    await createRecipeHistory(
      {
        recipe_id: Number(id),
        action: RecipeHistoryAction.updated,
        field_name: "recipe_visibility",
        old_value: JSON.stringify({
          public: existingRecipe.has_recipe_public_visibility,
          private: existingRecipe.has_recipe_private_visibility,
        }),
        new_value: JSON.stringify({
          public: has_recipe_public_visibility,
          private: has_recipe_private_visibility,
        }),
        description: `Recipe visibility updated to ${has_recipe_public_visibility ? "public" : "private"}`,
        ip_address: ipAddress,
        user_agent: userAgent,
        organization_id: organizationId,
        created_by: userId,
      },
      transaction
    );

    // Fetch updated recipe with all relations
    await transaction.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: res.__("RECIPE_VISIBILITY_UPDATED_SUCCESSFULLY"),
    });
  } catch (error: unknown) {
    if (!transaction.finished) {
      await transaction.rollback();
    }
    const customError = error as CustomError;

    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: res.__("ERROR_UPDATING_RECIPE_VISIBILITY"),
      error: customError.message || "Unknown error occurred",
    });
  }
};

/**
 * @description Manage Recipe User Assignments (Single API for all operations)
 * @route POST /api/v1/recipes/manage-assignments
 * @access Private (Admin only)
 */
const manageRecipeAssignments = async (
  req: Request,
  res: Response
): Promise<any> => {
  const transactionManager = new TransactionManager();

  try {
    const transaction = await transactionManager.start();
    const { recipe_id, user_ids = [] } = req.body;
    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;
    const user = (req as any).user;
    const ipAddress = req.ip || "Unknown";
    const userAgent = req.get("User-Agent") || "Unknown";

    // Validation
    if (!recipe_id) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: getTranslation(
          res,
          "RECIPE_ID_REQUIRED",
          "Recipe ID is required"
        ),
      });
    }

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: getTranslation(
          res,
          "UNAUTHORIZED_ACCESS",
          "Unauthorized access"
        ),
      });
    }

    // Check if user is super admin (system-wide access)
    const { isDefaultAccess } = await import("../helper/common");
    const isSuperAdmin = await isDefaultAccess(userId);

    // Check if user has permission to create recipes
    const hasPermission = await validateModulePermission(
      user,
      organizationId,
      MODULE_SLUGS.RECIPE,
      PERMISSION_TYPES.EDIT,
      getPlatformFromRequest(req)
    );

    if (!hasPermission) {
      return res.status(StatusCodes.FORBIDDEN).json({
        status: false,
        message: getTranslation(res, "PERMISSION_DENIED", "Permission denied"),
      });
    }

    // Build where clause for recipe lookup
    const recipeWhereClause: any = {
      id: recipe_id,
      recipe_status: {
        [Op.not]: RecipeStatus.deleted,
      },
    };

    // Super admins can access recipes from any organization
    if (!isSuperAdmin) {
      recipeWhereClause.organization_id = organizationId;
    }

    // Find existing recipe
    const existingRecipe = await Recipe.findOne({
      where: recipeWhereClause,
    });

    if (!existingRecipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: getTranslation(res, "RECIPE_NOT_FOUND", "Recipe not found"),
      });
    }

    // Validate recipe must be published
    if (existingRecipe.recipe_status !== RecipeStatus.publish) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: getTranslation(
          res,
          "RECIPE_MUST_BE_PUBLISHED",
          "Recipe must be published to assign users"
        ),
        current_status: existingRecipe.recipe_status,
      });
    }

    // Validate recipe must be private
    if (!existingRecipe.has_recipe_private_visibility) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: getTranslation(
          res,
          "RECIPE_MUST_BE_PRIVATE",
          "Recipe must be private to assign users"
        ),
        has_private_visibility: existingRecipe.has_recipe_private_visibility,
      });
    }

    let assignedCount = 0;

    if (user_ids.length === 0) {
      // Empty array means unassign all users from this recipe
      await RecipeUser.update(
        {
          status: RecipeUserStatus.inactive,
          updated_by: userId,
        },
        {
          where: {
            recipe_id: recipe_id,
            status: RecipeUserStatus.active,
          },
          transaction,
        }
      );

      // Create history record for unassign all
      await createRecipeHistory(
        {
          recipe_id: Number(recipe_id),
          action: RecipeHistoryAction.updated,
          field_name: "recipe_assignments",
          old_value: "assigned",
          new_value: "unassigned",
          description: `All users unassigned from recipe${isSuperAdmin ? " (Super Admin)" : ""}`,
          ip_address: ipAddress,
          user_agent: userAgent,
          organization_id: existingRecipe.organization_id,
          created_by: userId,
        },
        transaction
      );
    } else {
      // First, deactivate all current assignments
      await RecipeUser.update(
        {
          status: RecipeUserStatus.inactive,
          updated_by: userId,
        },
        {
          where: {
            recipe_id: recipe_id,
            status: RecipeUserStatus.active,
          },
          transaction,
        }
      );

      // Then assign only the users in the provided array
      const assignments = await Promise.all(
        user_ids.map(async (user_id: number) => {
          // Check if assignment already exists
          const existingAssignment = await RecipeUser.findOne({
            where: {
              recipe_id: recipe_id,
              user_id: user_id,
            },
            transaction,
          });

          if (existingAssignment) {
            // Reactivate existing assignment
            if (existingAssignment.status === RecipeUserStatus.inactive) {
              await existingAssignment.update(
                {
                  status: RecipeUserStatus.active,
                  updated_by: userId,
                },
                { transaction }
              );
              return { type: "reactivated", user_id };
            } else {
              // Already active
              return { type: "already_active", user_id };
            }
          } else {
            // Create new assignment
            await RecipeUser.create(
              {
                recipe_id: Number(recipe_id),
                user_id,
                status: RecipeUserStatus.active,
                organization_id: existingRecipe.organization_id,
                created_by: userId,
                updated_by: userId,
              },
              { transaction }
            );
            return { type: "created", user_id };
          }
        })
      );

      // Count results
      assignedCount = assignments.filter(
        (a) => a.type === "created" || a.type === "reactivated"
      ).length;

      // Create history record for assignments
      await createRecipeHistory(
        {
          recipe_id: Number(recipe_id),
          action: RecipeHistoryAction.updated,
          field_name: "recipe_assignments",
          new_value: JSON.stringify(user_ids),
          description: `Recipe assignments managed: ${user_ids.join(", ")}${isSuperAdmin ? " (Super Admin)" : ""}`,
          ip_address: ipAddress,
          user_agent: userAgent,
          organization_id: existingRecipe.organization_id,
          created_by: userId,
        },
        transaction
      );
    }

    await transactionManager.commit();

    return res.status(StatusCodes.OK).json({
      status: true,
      message: getTranslation(
        res,
        "RECIPE_ASSIGNMENTS_MANAGED_SUCCESSFULLY",
        "Recipe assignments managed successfully"
      ),
    });
  } catch (error: unknown) {
    await transactionManager.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error managing recipe assignments"
    );
  }
};

/**
 * @description Get Recipes Assigned to Current User
 * @route GET /api/v1/recipes/assigned-to-me
 * @access Private
 */
const getAssignedRecipes = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const {
      page = 1,
      limit = 20,
      status = "active",
      recipe_status,
      search,
      sort_by = "assigned_date",
      sort_order = "desc",
    } = req.query;
    const userId = (req as any).user?.id;
    const organizationId = (req as any).user?.organization_id;

    if (!userId) {
      return res.status(StatusCodes.UNAUTHORIZED).json({
        status: false,
        message: getTranslation(
          res,
          "UNAUTHORIZED_ACCESS",
          "Unauthorized access"
        ),
      });
    }

    const offset = (Number(page) - 1) * Number(limit);

    // Build where clause for RecipeUser
    const whereClause: any = {
      user_id: userId,
      status: status as RecipeUserStatus,
    };

    // Build where clause for Recipe
    const recipeWhereClause: any = {
      organization_id: organizationId,
      recipe_status: {
        [Op.not]: RecipeStatus.deleted,
      },
    };

    if (recipe_status) {
      recipeWhereClause.recipe_status = recipe_status;
    }

    if (search) {
      recipeWhereClause[Op.or] = [
        { recipe_title: { [Op.iLike]: `%${search}%` } },
        { recipe_description: { [Op.iLike]: `%${search}%` } },
      ];
    }

    // Determine sort order
    let orderClause: any;
    switch (sort_by) {
      case "recipe_title":
        orderClause = [
          { model: Recipe, as: "recipe" },
          "recipe_title",
          (sort_order as string)?.toUpperCase(),
        ];
        break;
      case "created_at":
        orderClause = [
          { model: Recipe, as: "recipe" },
          "created_at",
          (sort_order as string)?.toUpperCase(),
        ];
        break;
      case "assigned_date":
      default:
        orderClause = ["created_at", (sort_order as string)?.toUpperCase()];
        break;
    }

    const { count, rows } = await RecipeUser.findAndCountAll({
      where: whereClause,
      include: [
        {
          model: Recipe,
          as: "recipe",
          where: recipeWhereClause,
          attributes: [
            "id",
            "recipe_title",
            "recipe_description",
            "recipe_status",
            "has_recipe_public_visibility",
            "has_recipe_private_visibility",
            "recipe_complexity_level",
            "recipe_preparation_time",
            "recipe_cook_time",
            "created_at",
            "updated_at",
          ],
        },
      ],
      limit: Number(limit),
      offset,
      order: [orderClause],
    });

    const totalPages = Math.ceil(count / Number(limit));

    // Get highlight information for assigned recipes
    const recipeIds = rows.map((row: any) => row.recipe.id);
    const bulkHighlights = await getBulkRecipeHighlights(
      recipeIds,
      organizationId
    );

    // Add highlight information to each assigned recipe
    const rowsWithHighlights = rows.map((row: any) => {
      const recipeId = row.recipe.id;
      const recipeHighlight =
        (bulkHighlights.highlights as any)[recipeId] || null;

      return {
        ...row,
        recipe: {
          ...row.recipe,
          highlight: recipeHighlight,
          hasRecentChanges: !!recipeHighlight,
        },
      };
    });

    return res.status(StatusCodes.OK).json({
      status: true,
      message: getTranslation(
        res,
        "ASSIGNED_RECIPES_FETCHED_SUCCESSFULLY",
        "Assigned recipes fetched successfully"
      ),
      data: rowsWithHighlights,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total_records: count,
        total_pages: totalPages,
        has_next_page: Number(page) < totalPages,
        has_prev_page: Number(page) > 1,
      },
      highlight_metadata: bulkHighlights.metadata,
    });
  } catch (error: unknown) {
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error fetching assigned recipes"
    );
  }
};

/**
 * @description Import recipes from Excel file
 * @route POST /api/v1/recipes/import
 * @access Private
 */
const importRecipes = async (req: any, res: Response): Promise<any> => {
  const transaction = await sequelize.transaction();

  try {
    // Check if file is uploaded
    if (!req.file) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("NO_FILE_UPLOADED"),
      });
    }

    // Check if user has default access (admin users)
    const { isDefaultAccess } = await import("../helper/common");
    const hasDefaultAccess = await isDefaultAccess(req.user?.id);

    // Determine organization ID based on user access
    let organization_id;
    if (hasDefaultAccess) {
      // Admin users can specify organization_id in body or default to null for system-wide
      organization_id = req.body.organization_id || null;
    } else {
      // Regular users must use their organization_id
      organization_id = req.user?.organization_id;
      if (!organization_id) {
        return res.status(StatusCodes.BAD_REQUEST).json({
          status: false,
          message: res.__("ORGANIZATION_ID_REQUIRED"),
        });
      }
    }

    // Read Excel file
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(req.file.buffer);
    const worksheet = workbook.getWorksheet(1); // Get first worksheet

    if (!worksheet) {
      await transaction.rollback();
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("INVALID_EXCEL_FILE"),
      });
    }

    const results = {
      total: 0,
      success: 0,
      failed: 0,
      errors: [] as any[],
    };

    // Expected columns (row 1 should contain headers)
    const expectedHeaders = [
      "recipe_title",
      "recipe_public_title",
      "recipe_description",
      "recipe_preparation_time",
      "recipe_cook_time",
      "has_recipe_public_visibility",
      "has_recipe_private_visibility",
      "recipe_status",
      "recipe_complexity_level",
      "recipe_yield",
      "recipe_yield_unit",
      "recipe_total_portions",
      "recipe_single_portion_size",
      "recipe_serve_in",
      "recipe_garnish",
      "recipe_head_chef_tips",
      "recipe_foh_tips",
      "recipe_serving_method",
      "categories", // comma-separated category names
      "ingredients", // JSON string or formatted text with ingredient details
      "steps", // JSON string or formatted text with step details
      "nutrition_attributes", // comma-separated nutrition attribute names with units
      "allergen_attributes", // comma-separated allergen attribute names
      "cuisine_attributes", // comma-separated cuisine attribute names
      "dietary_attributes", // comma-separated dietary attribute names
      "haccp_attributes", // comma-separated HACCP attribute names with descriptions
    ];

    // Validate headers
    const headerRow = worksheet.getRow(1);
    const headers: string[] = [];
    headerRow.eachCell((cell, colNumber) => {
      headers[colNumber - 1] = String(cell.value).trim();
    });

    // Check if all expected headers are present
    const missingHeaders = expectedHeaders.filter(
      (header) => !headers.includes(header)
    );
    if (missingHeaders.length > 0) {
      await transaction.rollback();
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: res.__("MISSING_REQUIRED_HEADERS"),
        missing_headers: missingHeaders,
        expected_headers: expectedHeaders,
      });
    }

    // Process each row (starting from row 2)
    for (let rowNumber = 2; rowNumber <= worksheet.rowCount; rowNumber++) {
      const row = worksheet.getRow(rowNumber);

      // Skip empty rows
      if (row.hasValues) {
        results.total++;

        try {
          // Extract data from row
          const rowData: any = {};
          headers.forEach((header, index) => {
            const cell = row.getCell(index + 1);
            rowData[header] = cell.value;
          });

          // Validate required fields
          if (!rowData.recipe_title) {
            throw new Error("Recipe title is required");
          }

          // Generate unique slug
          const checkSlugExists = async (slug: string): Promise<boolean> => {
            const existingRecipe = await Recipe.findOne({
              where: {
                recipe_slug: slug,
                organization_id: organization_id,
                recipe_status: {
                  [Op.not]: RecipeStatus.deleted,
                },
              },
              transaction,
            });
            return !!existingRecipe;
          };

          const recipe_slug = await generateUniqueSlug(
            rowData.recipe_title,
            checkSlugExists,
            {
              maxLength: 25,
              separator: "-",
              lowercase: true,
            }
          );

          // Create main recipe
          const currentTimestamp = new Date();
          const recipeData = {
            recipe_title: rowData.recipe_title,
            recipe_public_title: rowData.recipe_public_title || null,
            recipe_description: rowData.recipe_description || null,
            recipe_preparation_time: rowData.recipe_preparation_time
              ? parseInt(String(rowData.recipe_preparation_time))
              : null,
            recipe_cook_time: rowData.recipe_cook_time
              ? parseInt(String(rowData.recipe_cook_time))
              : null,
            has_recipe_public_visibility:
              rowData.has_recipe_public_visibility === "true" ||
              rowData.has_recipe_public_visibility === true ||
              false,
            has_recipe_private_visibility:
              rowData.has_recipe_private_visibility === "true" ||
              rowData.has_recipe_private_visibility === true ||
              false,
            recipe_status: rowData.recipe_status || RecipeStatus.draft,
            recipe_complexity_level: rowData.recipe_complexity_level || null,
            recipe_yield: rowData.recipe_yield
              ? parseFloat(String(rowData.recipe_yield))
              : null,
            recipe_yield_unit: rowData.recipe_yield_unit || null,
            recipe_total_portions: rowData.recipe_total_portions
              ? parseInt(String(rowData.recipe_total_portions))
              : null,
            recipe_single_portion_size: rowData.recipe_single_portion_size
              ? parseFloat(String(rowData.recipe_single_portion_size))
              : null,
            recipe_serve_in: rowData.recipe_serve_in || null,
            recipe_garnish: rowData.recipe_garnish || null,
            recipe_head_chef_tips: rowData.recipe_head_chef_tips || null,
            recipe_foh_tips: rowData.recipe_foh_tips || null,
            recipe_serving_method: rowData.recipe_serving_method || null,
            recipe_slug,
            ingredient_costs_updated_at: currentTimestamp,
            nutrition_values_updated_at: currentTimestamp,
            organization_id: organization_id,
            created_by: req.user.id,
            updated_by: req.user.id,
          };

          const newRecipe = await Recipe.create(recipeData, { transaction });

          // Process categories if provided
          if (rowData.categories) {
            const categoryNames = String(rowData.categories)
              .split(",")
              .map((name) => name.trim())
              .filter((name) => name);
            if (categoryNames.length > 0) {
              const categoryData = [];
              for (const categoryName of categoryNames) {
                // Find category by name
                const category = await db.Category.findOne({
                  where: {
                    [Op.and]: [
                      {
                        [Op.or]: [
                          { category_name: categoryName },
                          {
                            category_slug: categoryName
                              .toLowerCase()
                              .replace(/\s+/g, "-"),
                          },
                        ],
                      },
                      {
                        category_type: "recipe",
                      },
                      {
                        [Op.or]: [
                          { organization_id },
                          { organization_id: null },
                          { is_system_category: true },
                        ],
                      },
                    ],
                  },
                  transaction,
                });

                if (category) {
                  categoryData.push({
                    recipe_id: newRecipe.id,
                    category_id: category.id,
                    status: "active",
                    organization_id: organization_id,
                    created_by: req.user.id,
                    updated_by: req.user.id,
                  });
                }
              }

              if (categoryData.length > 0) {
                await db.RecipeCategory.bulkCreate(categoryData, {
                  transaction,
                });
              }
            }
          }

          // Process ingredients if provided (format: "ingredient_name:quantity:unit_name")
          if (rowData.ingredients) {
            const ingredientEntries = String(rowData.ingredients)
              .split(",")
              .map((entry) => entry.trim())
              .filter((entry) => entry);
            if (ingredientEntries.length > 0) {
              const ingredientData = [];
              for (const ingredientEntry of ingredientEntries) {
                const parts = ingredientEntry.split(":");
                if (parts.length >= 2) {
                  const ingredientName = parts[0].trim();
                  const quantity = parseFloat(parts[1].trim());
                  const unitName = parts[2] ? parts[2].trim() : null;

                  // Find ingredient by name
                  const ingredient = await db.Ingredient.findOne({
                    where: {
                      [Op.and]: [
                        {
                          [Op.or]: [
                            { ingredient_name: ingredientName },
                            {
                              ingredient_slug: ingredientName
                                .toLowerCase()
                                .replace(/\s+/g, "-"),
                            },
                          ],
                        },
                        {
                          [Op.or]: [
                            { organization_id },
                            { organization_id: null },
                          ],
                        },
                      ],
                    },
                    transaction,
                  });

                  if (ingredient) {
                    let measureId = null;
                    if (unitName) {
                      // Find unit by name
                      const unit = await db.RecipeMeasure.findOne({
                        where: {
                          [Op.and]: [
                            {
                              [Op.or]: [
                                { unit_title: unitName },
                                {
                                  unit_slug: unitName
                                    .toLowerCase()
                                    .replace(/\s+/g, "-"),
                                },
                              ],
                            },
                            {
                              [Op.or]: [
                                { organization_id },
                                { organization_id: null },
                                { is_system_unit: true },
                              ],
                            },
                          ],
                        },
                        transaction,
                      });
                      if (unit) {
                        measureId = unit.id;
                      }
                    }

                    ingredientData.push({
                      recipe_id: newRecipe.id,
                      ingredient_id: ingredient.id,
                      ingredient_quantity: quantity,
                      ingredient_measure: measureId,
                      recipe_ingredient_status: "active",
                      organization_id: organization_id,
                      created_by: req.user.id,
                      updated_by: req.user.id,
                    });
                  }
                }
              }

              if (ingredientData.length > 0) {
                await db.RecipeIngredients.bulkCreate(ingredientData, {
                  transaction,
                });
              }
            }
          }

          // Process steps if provided (format: "1. Step description|2. Another step")
          if (rowData.steps) {
            const stepEntries = String(rowData.steps)
              .split("|")
              .map((step) => step.trim())
              .filter((step) => step);
            if (stepEntries.length > 0) {
              const stepData: any[] = [];
              stepEntries.forEach((stepDescription, index) => {
                // Remove step number if present (e.g., "1. " or "Step 1: ")
                const cleanDescription = stepDescription
                  .replace(/^\d+\.\s*/, "")
                  .replace(/^Step\s+\d+:\s*/i, "")
                  .trim();

                stepData.push({
                  recipe_id: newRecipe.id,
                  recipe_step_order: index + 1,
                  recipe_step_description: cleanDescription,
                  status: "active",
                  organization_id: organization_id,
                  created_by: req.user.id,
                  updated_by: req.user.id,
                });
              });

              if (stepData.length > 0) {
                await db.RecipeSteps.bulkCreate(stepData, { transaction });
              }
            }
          }

          // Process nutrition attributes if provided (format: "calories:350:kcal,protein:12:g")
          if (rowData.nutrition_attributes) {
            const nutritionEntries = String(rowData.nutrition_attributes)
              .split(",")
              .map((entry) => entry.trim())
              .filter((entry) => entry);
            if (nutritionEntries.length > 0) {
              const nutritionData: any[] = [];
              for (const nutritionEntry of nutritionEntries) {
                const parts = nutritionEntry.split(":");
                if (parts.length >= 2) {
                  const attributeName = parts[0].trim();
                  const value = parseFloat(parts[1].trim());
                  const unit = parts[2] ? parts[2].trim() : null;

                  // Find nutrition attribute by name
                  const attribute = await db.FoodAttributes.findOne({
                    where: {
                      [Op.and]: [
                        {
                          [Op.or]: [
                            { attribute_title: attributeName },
                            {
                              attribute_slug: attributeName
                                .toLowerCase()
                                .replace(/\s+/g, "-"),
                            },
                          ],
                        },
                        {
                          attribute_type: "nutrition",
                        },
                        {
                          [Op.or]: [
                            { organization_id },
                            { organization_id: null },
                            { is_system_attribute: true },
                          ],
                        },
                      ],
                    },
                    transaction,
                  });

                  if (attribute) {
                    nutritionData.push({
                      recipe_id: newRecipe.id,
                      attributes_id: attribute.id,
                      unit_of_measure: unit,
                      unit: value,
                      status: "active",
                      organization_id: organization_id,
                      created_by: req.user.id,
                      updated_by: req.user.id,
                    });
                  }
                }
              }

              if (nutritionData.length > 0) {
                await db.RecipeAttributes.bulkCreate(nutritionData, {
                  transaction,
                });
              }
            }
          }

          // Process allergen attributes if provided (comma-separated)
          if (rowData.allergen_attributes) {
            const allergenNames = String(rowData.allergen_attributes)
              .split(",")
              .map((name) => name.trim())
              .filter((name) => name);
            if (allergenNames.length > 0) {
              const allergenData: any[] = [];
              for (const allergenName of allergenNames) {
                // Find allergen attribute by name
                const attribute = await db.FoodAttributes.findOne({
                  where: {
                    [Op.and]: [
                      {
                        [Op.or]: [
                          { attribute_title: allergenName },
                          {
                            attribute_slug: allergenName
                              .toLowerCase()
                              .replace(/\s+/g, "-"),
                          },
                        ],
                      },
                      {
                        attribute_type: "allergen",
                      },
                      {
                        [Op.or]: [
                          { organization_id },
                          { organization_id: null },
                          { is_system_attribute: true },
                        ],
                      },
                    ],
                  },
                  transaction,
                });

                if (attribute) {
                  allergenData.push({
                    recipe_id: newRecipe.id,
                    attributes_id: attribute.id,
                    may_contain: false,
                    use_default: false,
                    status: "active",
                    organization_id: organization_id,
                    created_by: req.user.id,
                    updated_by: req.user.id,
                  });
                }
              }

              if (allergenData.length > 0) {
                await db.RecipeAttributes.bulkCreate(allergenData, {
                  transaction,
                });
              }
            }
          }

          // Process cuisine attributes if provided (comma-separated)
          if (rowData.cuisine_attributes) {
            const cuisineNames = String(rowData.cuisine_attributes)
              .split(",")
              .map((name) => name.trim())
              .filter((name) => name);
            if (cuisineNames.length > 0) {
              const cuisineData: any[] = [];
              for (const cuisineName of cuisineNames) {
                // Find cuisine attribute by name
                const attribute = await db.FoodAttributes.findOne({
                  where: {
                    [Op.and]: [
                      {
                        [Op.or]: [
                          { attribute_title: cuisineName },
                          {
                            attribute_slug: cuisineName
                              .toLowerCase()
                              .replace(/\s+/g, "-"),
                          },
                        ],
                      },
                      {
                        attribute_type: "cuisine",
                      },
                      {
                        [Op.or]: [
                          { organization_id },
                          { organization_id: null },
                          { is_system_attribute: true },
                        ],
                      },
                    ],
                  },
                  transaction,
                });

                if (attribute) {
                  cuisineData.push({
                    recipe_id: newRecipe.id,
                    attributes_id: attribute.id,
                    may_contain: false,
                    use_default: false,
                    status: "active",
                    organization_id: organization_id,
                    created_by: req.user.id,
                    updated_by: req.user.id,
                  });
                }
              }

              if (cuisineData.length > 0) {
                await db.RecipeAttributes.bulkCreate(cuisineData, {
                  transaction,
                });
              }
            }
          }

          // Process dietary attributes if provided (comma-separated)
          if (rowData.dietary_attributes) {
            const dietaryNames = String(rowData.dietary_attributes)
              .split(",")
              .map((name) => name.trim())
              .filter((name) => name);
            if (dietaryNames.length > 0) {
              const dietaryData: any[] = [];
              for (const dietaryName of dietaryNames) {
                // Find dietary attribute by name
                const attribute = await db.FoodAttributes.findOne({
                  where: {
                    [Op.and]: [
                      {
                        [Op.or]: [
                          { attribute_title: dietaryName },
                          {
                            attribute_slug: dietaryName
                              .toLowerCase()
                              .replace(/\s+/g, "-"),
                          },
                        ],
                      },
                      {
                        attribute_type: "dietary",
                      },
                      {
                        [Op.or]: [
                          { organization_id },
                          { organization_id: null },
                          { is_system_attribute: true },
                        ],
                      },
                    ],
                  },
                  transaction,
                });

                if (attribute) {
                  dietaryData.push({
                    recipe_id: newRecipe.id,
                    attributes_id: attribute.id,
                    may_contain: false,
                    use_default: false,
                    status: "active",
                    organization_id: organization_id,
                    created_by: req.user.id,
                    updated_by: req.user.id,
                  });
                }
              }

              if (dietaryData.length > 0) {
                await db.RecipeAttributes.bulkCreate(dietaryData, {
                  transaction,
                });
              }
            }
          }

          // Process HACCP attributes if provided (format: "temperature control:keep hot,hygiene:wash hands")
          if (rowData.haccp_attributes) {
            const haccpEntries = String(rowData.haccp_attributes)
              .split(",")
              .map((entry) => entry.trim())
              .filter((entry) => entry);
            if (haccpEntries.length > 0) {
              const haccpData: any[] = [];
              for (const haccpEntry of haccpEntries) {
                const parts = haccpEntry.split(":");
                const attributeName = parts[0].trim();
                const description = parts[1] ? parts[1].trim() : null;

                // Find HACCP attribute by name
                const attribute = await db.FoodAttributes.findOne({
                  where: {
                    [Op.and]: [
                      {
                        [Op.or]: [
                          { attribute_title: attributeName },
                          {
                            attribute_slug: attributeName
                              .toLowerCase()
                              .replace(/\s+/g, "-"),
                          },
                        ],
                      },
                      {
                        attribute_type: "haccp_category",
                      },
                      {
                        [Op.or]: [
                          { organization_id },
                          { organization_id: null },
                          { is_system_attribute: true },
                        ],
                      },
                    ],
                  },
                  transaction,
                });

                if (attribute) {
                  haccpData.push({
                    recipe_id: newRecipe.id,
                    attributes_id: attribute.id,
                    attribute_description: description,
                    may_contain: false,
                    use_default: false,
                    status: "active",
                    organization_id: organization_id,
                    created_by: req.user.id,
                    updated_by: req.user.id,
                  });
                }
              }

              if (haccpData.length > 0) {
                await db.RecipeAttributes.bulkCreate(haccpData, {
                  transaction,
                });
              }
            }
          }

          // Create recipe history entry
          await createRecipeHistory(
            {
              recipe_id: newRecipe.id,
              action: RecipeHistoryAction.created,
              field_name: "recipe_import",
              description: `Recipe "${rowData.recipe_title}" was imported from Excel file.`,
              ip_address: req.ip,
              user_agent: req.get("User-Agent") || "",
              organization_id: organization_id,
              created_by: req.user.id,
            },
            transaction
          );

          // Log successful import
          console.log(
            `Row ${rowNumber}: Successfully imported recipe '${rowData.recipe_title}'`
          );
          results.success++;
        } catch (error: any) {
          results.failed++;
          results.errors.push({
            row: rowNumber,
            error: error.message,
          });

          // For critical errors, rollback entire transaction
          if (
            error.name === "SequelizeConnectionError" ||
            error.name === "SequelizeDatabaseError"
          ) {
            throw error;
          }

          // For validation errors, continue with other rows
          console.warn(`Row ${rowNumber} failed validation:`, error.message);
        }
      }
    }

    await transaction.commit();

    // Add user-friendly summary message based on results
    let message;
    if (results.failed === 0) {
      message = `Successfully imported ${results.success} recipes!`;
    } else if (results.success === 0) {
      message = `Import failed. ${results.failed} recipes could not be imported.`;
    } else {
      message = `Import completed. ${results.success} recipes imported successfully, ${results.failed} failed.`;
    }

    return res.status(StatusCodes.OK).json({
      status: true,
      message,
      results,
      summary: {
        total_processed: results.total,
        successful_imports: results.success,
        failed_imports: results.failed,
        success_rate:
          results.total > 0
            ? Math.round((results.success / results.total) * 100)
            : 0,
      },
    });
  } catch (error: unknown) {
    await transaction.rollback();
    return ErrorHandler.createErrorResponse(
      error,
      res,
      "Error importing recipes"
    );
  }
};

/**
 * @description Get recipe highlight information
 * @route GET /api/v1/private/recipes/:id/highlight
 * @access Private
 */
const getRecipeHighlightController = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const { id } = req.params;
    const organizationId = (req as any).user?.organization_id;
    const userId = (req as any).user?.id;

    // Validate recipe ID
    const recipeId = parseInt(id);
    if (isNaN(recipeId)) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Invalid recipe ID",
      });
    }

    // Check if recipe exists and user has access
    const recipe = await Recipe.findOne({
      where: {
        id: recipeId,
        organization_id: organizationId,
        recipe_status: {
          [Op.not]: RecipeStatus.deleted,
        },
      },
    });

    if (!recipe) {
      return res.status(StatusCodes.NOT_FOUND).json({
        status: false,
        message: res.__("RECIPE_NOT_FOUND"),
      });
    }

    // Get highlight information
    const highlightResult = await getRecipeHighlight(recipeId, organizationId);
    const hasRecent = await hasRecentChanges(recipeId, organizationId);

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Recipe highlight information retrieved successfully",
      data: {
        recipe_id: recipeId,
        highlight: highlightResult ? highlightResult.highlight : null,
        hasRecentChanges: hasRecent,
      },
    });
  } catch (error: unknown) {
    const customError = error as CustomError;
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: "Error retrieving recipe highlight information",
      error: customError.message,
    });
  }
};

/**
 * @description Get bulk recipe highlights
 * @route POST /api/v1/private/recipes/bulk-highlights
 * @access Private
 */
const getBulkRecipeHighlightsController = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const { recipe_ids } = req.body;
    const organizationId = (req as any).user?.organization_id;

    // Validate recipe IDs
    if (!Array.isArray(recipe_ids) || recipe_ids.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Recipe IDs array is required and must not be empty",
      });
    }

    const recipeIds = recipe_ids
      .map((id: any) => parseInt(id))
      .filter((id: number) => !isNaN(id));

    if (recipeIds.length === 0) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "No valid recipe IDs provided",
      });
    }

    // Get bulk highlights
    const bulkHighlights = await getBulkRecipeHighlights(
      recipeIds,
      organizationId
    );

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Bulk recipe highlights retrieved successfully",
      data: bulkHighlights.highlights,
      metadata: bulkHighlights.metadata,
    });
  } catch (error: unknown) {
    const customError = error as CustomError;
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: "Error retrieving bulk recipe highlights",
      error: customError.message,
    });
  }
};

/**
 * @description Get detailed attribute information by IDs
 * @route GET /api/v1/private/recipes/attributes/details
 * @access Private
 */
const getAttributeDetails = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const { attribute_ids } = req.query;
    const organizationId = (req as any).user?.organization_id;

    // Validate attribute IDs
    if (!attribute_ids) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Attribute IDs are required",
      });
    }

    // Parse attribute IDs
    let attributeIdArray: number[];
    try {
      if (typeof attribute_ids === "string") {
        attributeIdArray = attribute_ids
          .split(",")
          .map((id) => parseInt(id.trim()));
      } else if (Array.isArray(attribute_ids)) {
        attributeIdArray = attribute_ids.map((id) => parseInt(String(id)));
      } else {
        throw new Error("Invalid format");
      }

      // Validate that all IDs are valid numbers
      if (attributeIdArray.some((id) => isNaN(id))) {
        throw new Error("Invalid attribute ID format");
      }
    } catch (error) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message:
          "Invalid attribute IDs format. Please provide comma-separated numbers.",
      });
    }

    // Get attribute details
    const attributeDetails = await db.sequelize.query(
      `
      SELECT
        fa.id,
        fa.attribute_title,
        fa.attribute_slug,
        fa.attribute_type,
        fa.attribute_description,
        fa.attribute_status,
        i.id as icon_id,
        i.item_name as icon_name,
        i.item_location as icon_location,
        i.item_mime_type as icon_mime_type,
        CASE WHEN i.item_location IS NOT NULL
          THEN CONCAT('${process.env.BASE_URL || "http://localhost:3000"}/backend-api/v1/public/user/get-file?location=', i.item_location)
          ELSE NULL
        END as icon_url
      FROM mo_food_attributes fa
      LEFT JOIN nv_items i ON fa.attribute_icon = i.id
      WHERE fa.id IN (${attributeIdArray.join(",")})
        AND fa.attribute_status = 'active'
        ${organizationId ? `AND (fa.organization_id = '${organizationId}' OR fa.organization_id IS NULL)` : ""}
      ORDER BY fa.attribute_type, fa.attribute_title
    `,
      {
        type: db.sequelize.QueryTypes.SELECT,
      }
    );

    // Format the response with consistent field names matching get recipe by id API
    const formattedAttributes = attributeDetails.map((attr: any) => ({
      id: attr.id,
      attribute_title: attr.attribute_title, // ✅ Consistent with get recipe by id
      attribute_slug: attr.attribute_slug, // ✅ Consistent with get recipe by id
      attribute_type: attr.attribute_type, // ✅ Consistent with get recipe by id
      attribute_description: attr.attribute_description,
      attribute_status: attr.attribute_status,
      item_detail: attr.icon_id
        ? {
          item_id: attr.icon_id,
          item_name: attr.icon_name,
          item_location: attr.icon_location,
          item_mime_type: attr.icon_mime_type,
          item_url: attr.icon_url,
        }
        : {},
    }));

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Attribute details retrieved successfully",
      data: {
        attributes: formattedAttributes,
        total: formattedAttributes.length,
      },
    });
  } catch (error: unknown) {
    const customError = error as CustomError;
    console.error("Error in getAttributeDetails:", customError);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: "Internal server error",
      error: customError.message,
    });
  }
};

/**
 * @description Get detailed ingredient information by IDs
 * @route GET /api/v1/private/recipes/ingredients/details
 * @access Private
 */
const getIngredientDetails = async (
  req: Request,
  res: Response
): Promise<any> => {
  try {
    const { ingredient_ids } = req.query;
    const organizationId = (req as any).user?.organization_id;

    // Validate ingredient IDs
    if (!ingredient_ids) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message: "Ingredient IDs are required",
      });
    }

    // Parse ingredient IDs
    let ingredientIdArray: number[];
    try {
      if (typeof ingredient_ids === "string") {
        ingredientIdArray = ingredient_ids
          .split(",")
          .map((id) => parseInt(id.trim()));
      } else if (Array.isArray(ingredient_ids)) {
        ingredientIdArray = ingredient_ids.map((id) => parseInt(String(id)));
      } else {
        throw new Error("Invalid format");
      }

      // Validate that all IDs are valid numbers
      if (ingredientIdArray.some((id) => isNaN(id))) {
        throw new Error("Invalid ingredient ID format");
      }
    } catch (error) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        status: false,
        message:
          "Invalid ingredient IDs format. Please provide comma-separated numbers.",
      });
    }

    // Get ingredient details
    const ingredientDetails = await db.sequelize.query(
      `
      SELECT
        i.id,
        i.ingredient_name,
        i.ingredient_slug,
        i.ingredient_description,
        i.ingredient_status,
        i.waste_percentage,
        i.unit_of_measure,
        i.cost_per_unit,
        i.cost_last_updated_at,
        i.nutrition_last_updated_at
      FROM mo_ingredients i
      WHERE i.id IN (${ingredientIdArray.join(",")})
        AND i.ingredient_status = 'active'
        ${organizationId ? `AND (i.organization_id = '${organizationId}' OR i.organization_id IS NULL)` : ""}
      ORDER BY i.ingredient_name
    `,
      {
        type: db.sequelize.QueryTypes.SELECT,
      }
    );

    // Format the response
    const formattedIngredients = ingredientDetails.map((ing: any) => ({
      id: ing.id,
      name: ing.ingredient_name,
      slug: ing.ingredient_slug,
      description: ing.ingredient_description,
      status: ing.ingredient_status,
      waste_percentage: ing.waste_percentage,
      unit_of_measure: ing.unit_of_measure,
      cost_per_unit: ing.cost_per_unit,
      cost_last_updated_at: ing.cost_last_updated_at,
      nutrition_last_updated_at: ing.nutrition_last_updated_at,
    }));

    return res.status(StatusCodes.OK).json({
      status: true,
      message: "Ingredient details retrieved successfully",
      data: {
        ingredients: formattedIngredients,
        total: formattedIngredients.length,
      },
    });
  } catch (error: unknown) {
    const customError = error as CustomError;
    console.error("Error in getIngredientDetails:", customError);
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      status: false,
      message: "Internal server error",
      error: customError.message,
    });
  }
};

export default {
  createRecipe,
  getRecipeById,
  getPublicRecipeById,
  getPublicRecipesList,
  getPrivateRecipesList,
  updateRecipe,
  archiveRecipe,
  deleteRecipe,
  publishRecipe,
  makeRecipePublic,
  getRecipeHistory,
  duplicateRecipe,
  toggleRecipeBookmark,
  exportRecipe,
  manageRecipeAssignments,
  getAssignedRecipes,
  importRecipes,
  getRecipeHighlightController,
  getBulkRecipeHighlightsController,
  getAttributeDetails,
  getIngredientDetails,
};
